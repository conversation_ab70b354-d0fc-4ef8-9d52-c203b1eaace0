package cn.trasen.ams.common.interceptor.enhanced;

import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.BinaryExpression;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * SQL处理器
 * 负责SQL的解析、分析和修改
 * 
 * <AUTHOR>
 * @date 2025/9/4
 */
public class SqlProcessor {
    
    private static final Logger log = LoggerFactory.getLogger(SqlProcessor.class);
    
    private final InterceptorConfig config;
    
    public SqlProcessor(InterceptorConfig config) {
        this.config = config;
    }
    
    /**
     * 处理SQL语句，添加机构代码条件
     */
    public String processSql(String sql, String orgCode) {
        if (isBlankString(sql) || isBlankString(orgCode)) {
            return sql;
        }
        
        try {
            // 解析SQL语句
            Statement statement = CCJSqlParserUtil.parse(sql);
            
            // 只处理SELECT语句
            if (!(statement instanceof Select)) {
                if (config.isEnableLogging()) {
                    log.debug("非SELECT语句，跳过处理: {}", statement.getClass().getSimpleName());
                }
                return sql;
            }
            
            Select selectStatement = (Select) statement;
            SelectBody selectBody = selectStatement.getSelectBody();
            
            // 处理不同类型的SELECT语句
            boolean modified = processSelectBody(selectBody, orgCode);
            
            if (modified) {
                String modifiedSql = selectStatement.toString();
                
                if (config.isEnableLogging()) {
                    log.info("SQL修改完成，机构代码: {}", orgCode);
                    log.debug("修改后SQL: {}", modifiedSql);
                }
                
                return modifiedSql;
            }
            
            return sql;
            
        } catch (JSQLParserException e) {
            log.error("SQL解析失败: {}", e.getMessage());
            if (config.isEnableStrictMode()) {
                throw new RuntimeException("SQL解析失败", e);
            }
            return sql;
        } catch (Exception e) {
            log.error("处理SQL时发生异常", e);
            if (config.isEnableStrictMode()) {
                throw new RuntimeException("处理SQL时发生异常", e);
            }
            return sql;
        }
    }
    
    /**
     * 处理SelectBody
     */
    private boolean processSelectBody(SelectBody selectBody, String orgCode) {
        if (selectBody instanceof PlainSelect) {
            return processPlainSelect((PlainSelect) selectBody, orgCode);
        } else if (selectBody instanceof SetOperationList) {
            return processSetOperationList((SetOperationList) selectBody, orgCode);
        } else {
            if (config.isEnableLogging()) {
                log.debug("不支持的SELECT类型: {}", selectBody.getClass().getSimpleName());
            }
            return false;
        }
    }
    
    /**
     * 处理普通SELECT语句
     */
    private boolean processPlainSelect(PlainSelect plainSelect, String orgCode) {
        boolean modified = false;
        
        // 处理主表
        FromItem fromItem = plainSelect.getFromItem();
        if (fromItem instanceof Table) {
            Table table = (Table) fromItem;
            if (config.shouldAddOrgCodeCondition(table.getName())) {
                if (addOrgCodeConditionToMainTable(plainSelect, table, orgCode)) {
                    modified = true;
                }
            }
        } else if (fromItem instanceof SubSelect) {
            // 递归处理子查询
            SubSelect subSelect = (SubSelect) fromItem;
            SelectBody subSelectBody = subSelect.getSelectBody();
            if (processSelectBody(subSelectBody, orgCode)) {
                modified = true;
            }
        }
        
        // 处理JOIN中的表
        List<Join> joins = plainSelect.getJoins();
        if (joins != null) {
            for (Join join : joins) {
                FromItem rightItem = join.getRightItem();
                if (rightItem instanceof Table) {
                    Table joinTable = (Table) rightItem;
                    if (config.shouldAddOrgCodeCondition(joinTable.getName())) {
                        if (addOrgCodeConditionToJoin(join, joinTable, orgCode)) {
                            modified = true;
                        }
                    }
                } else if (rightItem instanceof SubSelect) {
                    // 递归处理JOIN中的子查询
                    SubSelect subSelect = (SubSelect) rightItem;
                    SelectBody subSelectBody = subSelect.getSelectBody();
                    if (processSelectBody(subSelectBody, orgCode)) {
                        modified = true;
                    }
                }
            }
        }
        
        return modified;
    }
    
    /**
     * 处理UNION等集合操作
     */
    private boolean processSetOperationList(SetOperationList setOperationList, String orgCode) {
        boolean modified = false;
        
        List<SelectBody> selects = setOperationList.getSelects();
        if (selects != null) {
            for (SelectBody selectBody : selects) {
                if (processSelectBody(selectBody, orgCode)) {
                    modified = true;
                }
            }
        }
        
        return modified;
    }
    
    /**
     * 为主表添加机构代码条件
     */
    private boolean addOrgCodeConditionToMainTable(PlainSelect plainSelect, Table table, String orgCode) {
        if (hasOrgCodeCondition(plainSelect.getWhere())) {
            if (config.isEnableLogging()) {
                log.debug("WHERE子句中已包含{}条件，跳过修改", InterceptorConfig.ORG_CODE_COLUMN);
            }
            return false;
        }
        
        // 创建机构代码条件
        Expression orgCodeCondition = createOrgCodeCondition(table, orgCode);
        
        // 添加到WHERE子句
        Expression existingWhere = plainSelect.getWhere();
        if (existingWhere == null) {
            plainSelect.setWhere(orgCodeCondition);
        } else {
            AndExpression andExpression = new AndExpression(orgCodeCondition, existingWhere);
            plainSelect.setWhere(andExpression);
        }
        
        if (config.isEnableLogging()) {
            log.debug("为表 {} 添加机构代码条件: {}", table.getName(), orgCode);
        }
        
        return true;
    }
    
    /**
     * 为JOIN表添加机构代码条件
     */
    private boolean addOrgCodeConditionToJoin(Join join, Table table, String orgCode) {
        Expression joinCondition = join.getOnExpression();
        
        if (hasOrgCodeCondition(joinCondition)) {
            if (config.isEnableLogging()) {
                log.debug("JOIN条件中已包含{}条件，跳过修改", InterceptorConfig.ORG_CODE_COLUMN);
            }
            return false;
        }
        
        // 创建机构代码条件
        Expression orgCodeCondition = createOrgCodeCondition(table, orgCode);
        
        // 添加到JOIN条件
        if (joinCondition == null) {
            join.setOnExpression(orgCodeCondition);
        } else {
            AndExpression andExpression = new AndExpression(joinCondition, orgCodeCondition);
            join.setOnExpression(andExpression);
        }
        
        if (config.isEnableLogging()) {
            log.debug("为JOIN表 {} 添加机构代码条件: {}", table.getName(), orgCode);
        }
        
        return true;
    }
    
    /**
     * 创建机构代码条件表达式
     */
    private Expression createOrgCodeCondition(Table table, String orgCode) {
        // 构建列引用
        Column orgCodeColumn = new Column();
        if (table.getAlias() != null) {
            orgCodeColumn.setTable(table.getAlias());
        } else {
            orgCodeColumn.setTable(table);
        }
        orgCodeColumn.setColumnName(InterceptorConfig.ORG_CODE_COLUMN);
        
        // 构建等值条件
        EqualsTo equalsTo = new EqualsTo();
        equalsTo.setLeftExpression(orgCodeColumn);
        equalsTo.setRightExpression(new StringValue(orgCode));
        
        return equalsTo;
    }
    
    /**
     * 检查表达式中是否已包含机构代码条件
     */
    private boolean hasOrgCodeCondition(Expression expression) {
        if (expression == null) {
            return false;
        }
        
        return containsOrgCodeColumn(expression);
    }
    
    /**
     * 递归检查表达式中是否包含机构代码列
     */
    private boolean containsOrgCodeColumn(Expression expression) {
        if (expression instanceof Column) {
            Column column = (Column) expression;
            return InterceptorConfig.ORG_CODE_COLUMN.equalsIgnoreCase(column.getColumnName());
        } else if (expression instanceof BinaryExpression) {
            BinaryExpression binaryExpression = (BinaryExpression) expression;
            return containsOrgCodeColumn(binaryExpression.getLeftExpression()) ||
                   containsOrgCodeColumn(binaryExpression.getRightExpression());
        }
        
        return false;
    }
    
    /**
     * 检查字符串是否为空或空白
     */
    private boolean isBlankString(String str) {
        return str == null || str.trim().isEmpty();
    }
}
