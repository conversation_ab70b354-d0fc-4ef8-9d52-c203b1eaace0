package cn.trasen.ams.common.interceptor.enhanced;

import java.util.HashSet;
import java.util.Set;

/**
 * 拦截器配置类
 * 集中管理拦截器的配置信息
 * 
 * <AUTHOR>
 * @date 2025/9/4
 */
public class InterceptorConfig {
    
    /**
     * 机构代码字段名
     */
    public static final String ORG_CODE_COLUMN = "sso_org_code";
    
    /**
     * 需要排除的表（不添加机构代码条件）
     */
    private static final Set<String> DEFAULT_EXCLUDE_TABLES = new HashSet<>();
    static {
        DEFAULT_EXCLUDE_TABLES.add("d_category22");
        // 可以继续添加其他需要排除的表
    }
    
    /**
     * 需要添加机构代码条件的表前缀
     */
    private static final Set<String> DEFAULT_INCLUDE_TABLE_PREFIXES = new HashSet<>();
    static {
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("d_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("m_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("call_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("civil_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("comm_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("cust_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("dept_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("device_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("di_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("dp_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("emp_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("gov_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("hr_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("hrms_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("importdata_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("jc_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("kq_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("med_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("new_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("political_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("satisfaction_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("scheduling_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("sms_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("t_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("tbl_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("thr_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("toa_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("user_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("wf_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("ws_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("zdy_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("zp_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("zt_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("ts_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("c_");
        DEFAULT_INCLUDE_TABLE_PREFIXES.add("thps_");
    }
    
    private Set<String> excludeTables;
    private Set<String> includeTablePrefixes;
    private boolean enableSqlModification = true;
    private boolean enableLogging = true;
    private boolean enableStrictMode = true;
    
    public InterceptorConfig() {
        this.excludeTables = new HashSet<>(DEFAULT_EXCLUDE_TABLES);
        this.includeTablePrefixes = new HashSet<>(DEFAULT_INCLUDE_TABLE_PREFIXES);
    }
    
    /**
     * 判断是否需要添加机构代码条件
     */
    public boolean shouldAddOrgCodeCondition(String tableName) {
        if (tableName == null || tableName.trim().isEmpty()) {
            return false;
        }
        
        String pureTableName = getPureTableName(tableName);
        
        // 检查排除列表
        if (excludeTables.contains(pureTableName.toLowerCase())) {
            return false;
        }
        
        // 检查包含前缀
        return includeTablePrefixes.stream()
                .anyMatch(prefix -> pureTableName.toLowerCase().startsWith(prefix.toLowerCase()));
    }
    
    /**
     * 获取纯表名（去除schema和引号）
     */
    private String getPureTableName(String tableName) {
        if (tableName == null || tableName.trim().isEmpty()) {
            return tableName;
        }
        
        String pureTableName = tableName;
        
        // 去除schema前缀
        if (tableName.contains(".")) {
            String[] parts = tableName.split("\\.");
            pureTableName = parts[parts.length - 1];
        }
        
        // 去除引号
        if (pureTableName.startsWith("`") && pureTableName.endsWith("`")) {
            pureTableName = pureTableName.substring(1, pureTableName.length() - 1);
        }
        if (pureTableName.startsWith("\"") && pureTableName.endsWith("\"")) {
            pureTableName = pureTableName.substring(1, pureTableName.length() - 1);
        }
        
        return pureTableName;
    }
    
    /**
     * 添加排除表
     */
    public void addExcludeTable(String tableName) {
        if (tableName != null && !tableName.trim().isEmpty()) {
            excludeTables.add(tableName.toLowerCase());
        }
    }
    
    /**
     * 移除排除表
     */
    public void removeExcludeTable(String tableName) {
        if (tableName != null) {
            excludeTables.remove(tableName.toLowerCase());
        }
    }
    
    /**
     * 添加包含表前缀
     */
    public void addIncludeTablePrefix(String prefix) {
        if (prefix != null && !prefix.trim().isEmpty()) {
            includeTablePrefixes.add(prefix.toLowerCase());
        }
    }
    
    /**
     * 移除包含表前缀
     */
    public void removeIncludeTablePrefix(String prefix) {
        if (prefix != null) {
            includeTablePrefixes.remove(prefix.toLowerCase());
        }
    }
    
    // Getters and Setters
    public Set<String> getExcludeTables() {
        return new HashSet<>(excludeTables);
    }
    
    public void setExcludeTables(Set<String> excludeTables) {
        this.excludeTables = excludeTables != null ? new HashSet<>(excludeTables) : new HashSet<>();
    }
    
    public Set<String> getIncludeTablePrefixes() {
        return new HashSet<>(includeTablePrefixes);
    }
    
    public void setIncludeTablePrefixes(Set<String> includeTablePrefixes) {
        this.includeTablePrefixes = includeTablePrefixes != null ? new HashSet<>(includeTablePrefixes) : new HashSet<>();
    }
    
    public boolean isEnableSqlModification() {
        return enableSqlModification;
    }
    
    public void setEnableSqlModification(boolean enableSqlModification) {
        this.enableSqlModification = enableSqlModification;
    }
    
    public boolean isEnableLogging() {
        return enableLogging;
    }
    
    public void setEnableLogging(boolean enableLogging) {
        this.enableLogging = enableLogging;
    }
    
    public boolean isEnableStrictMode() {
        return enableStrictMode;
    }
    
    public void setEnableStrictMode(boolean enableStrictMode) {
        this.enableStrictMode = enableStrictMode;
    }
    
    @Override
    public String toString() {
        return "InterceptorConfig{" +
                "excludeTables=" + excludeTables.size() + " tables" +
                ", includeTablePrefixes=" + includeTablePrefixes.size() + " prefixes" +
                ", enableSqlModification=" + enableSqlModification +
                ", enableLogging=" + enableLogging +
                ", enableStrictMode=" + enableStrictMode +
                '}';
    }
}
