package cn.trasen.ams.common.interceptor.enhanced;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.plugin.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.Properties;

/**
 * 增强版多机构拦截器 - 基于JSQLParser 4.9实现
 * 提供更安全、严谨的SQL解析和修改功能
 *
 * 主要改进：
 * 1. 使用JSQLParser进行严格的SQL解析，避免正则表达式的不准确性
 * 2. 支持复杂SQL语句（子查询、JOIN、UNION等）
 * 3. 更好的错误处理和日志记录
 * 4. 模块化设计，便于维护和扩展
 * 5. 配置化管理，支持动态调整
 *
 * <AUTHOR>
 * @date 2025/9/4
 */
@Intercepts({
    @Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})
})
public class EnhancedSsoOrgCodeInterceptor implements Interceptor {

    private static final Logger log = LoggerFactory.getLogger(EnhancedSsoOrgCodeInterceptor.class);

    private InterceptorConfig config;
    private SqlProcessor sqlProcessor;

    public EnhancedSsoOrgCodeInterceptor() {
        this.config = new InterceptorConfig();
        this.sqlProcessor = new SqlProcessor(config);
    }

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        long startTime = System.currentTimeMillis();

        if (config.isEnableLogging()) {
            log.info("=== 增强版多机构拦截器开始执行 ===");
        }

        try {
            StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
            String originalSql = statementHandler.getBoundSql().getSql();

            if (config.isEnableLogging()) {
                log.debug("原始SQL: {}", originalSql);
            }

            // 获取用户信息
            ThpsUser user = UserInfoHolder.getCurrentUserInfo();
            if (user == null || isBlankString(user.getCorpcode())) {
                if (config.isEnableLogging()) {
                    log.warn("无法获取用户信息或机构代码为空，跳过SQL修改");
                }
                return invocation.proceed();
            }

            // 处理SQL
            String modifiedSql = sqlProcessor.processSql(originalSql, user.getCorpcode());

            // 应用SQL修改
            if (!originalSql.equals(modifiedSql) && config.isEnableSqlModification()) {
                applySqlModification(statementHandler, modifiedSql);
            }

            Object result = invocation.proceed();

            if (config.isEnableLogging()) {
                long executionTime = System.currentTimeMillis() - startTime;
                log.info("=== 增强版多机构拦截器执行完成，耗时: {}ms ===", executionTime);
            }

            return result;

        } catch (Exception e) {
            log.error("增强版多机构拦截器执行异常", e);
            if (config.isEnableStrictMode()) {
                throw e;
            }
            return invocation.proceed();
        }
    }

    /**
     * 应用SQL修改
     */
    private void applySqlModification(StatementHandler statementHandler, String modifiedSql) {
        try {
            Object boundSql = statementHandler.getBoundSql();

            // 使用反射修改SQL
            java.lang.reflect.Field sqlField = boundSql.getClass().getDeclaredField("sql");
            sqlField.setAccessible(true);
            sqlField.set(boundSql, modifiedSql);

            if (config.isEnableLogging()) {
                log.debug("SQL修改成功");
            }

        } catch (Exception e) {
            log.error("应用SQL修改失败", e);
            if (config.isEnableStrictMode()) {
                throw new RuntimeException("应用SQL修改失败", e);
            }
        }
    }

    /**
     * 检查字符串是否为空或空白
     */
    private boolean isBlankString(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * 为主表添加机构代码条件
     */
    private void addOrgCodeCondition(PlainSelect plainSelect, Table table, String orgCode) {
        if (hasOrgCodeCondition(plainSelect.getWhere())) {
            if (enableLogging) {
                log.debug("WHERE子句中已包含{}条件，跳过修改", ORG_CODE_COLUMN);
            }
            return;
        }
        
        // 创建机构代码条件
        Expression orgCodeCondition = createOrgCodeCondition(table, orgCode);
        
        // 添加到WHERE子句
        Expression existingWhere = plainSelect.getWhere();
        if (existingWhere == null) {
            plainSelect.setWhere(orgCodeCondition);
        } else {
            AndExpression andExpression = new AndExpression(orgCodeCondition, existingWhere);
            plainSelect.setWhere(andExpression);
        }
        
        if (enableLogging) {
            log.debug("为表 {} 添加机构代码条件: {}", table.getName(), orgCode);
        }
    }
    
    /**
     * 为JOIN表添加机构代码条件
     */
    private void addOrgCodeConditionToJoin(Join join, Table table, String orgCode) {
        Expression joinCondition = join.getOnExpression();
        
        if (hasOrgCodeCondition(joinCondition)) {
            if (enableLogging) {
                log.debug("JOIN条件中已包含{}条件，跳过修改", ORG_CODE_COLUMN);
            }
            return;
        }
        
        // 创建机构代码条件
        Expression orgCodeCondition = createOrgCodeCondition(table, orgCode);
        
        // 添加到JOIN条件
        if (joinCondition == null) {
            join.setOnExpression(orgCodeCondition);
        } else {
            AndExpression andExpression = new AndExpression(joinCondition, orgCodeCondition);
            join.setOnExpression(andExpression);
        }
        
        if (enableLogging) {
            log.debug("为JOIN表 {} 添加机构代码条件: {}", table.getName(), orgCode);
        }
    }
    
    /**
     * 创建机构代码条件表达式
     */
    private Expression createOrgCodeCondition(Table table, String orgCode) {
        // 构建列引用
        Column orgCodeColumn = new Column();
        if (table.getAlias() != null) {
            orgCodeColumn.setTable(table.getAlias());
        } else {
            orgCodeColumn.setTable(table);
        }
        orgCodeColumn.setColumnName(ORG_CODE_COLUMN);
        
        // 构建等值条件
        EqualsTo equalsTo = new EqualsTo();
        equalsTo.setLeftExpression(orgCodeColumn);
        equalsTo.setRightExpression(new StringValue(orgCode));
        
        return equalsTo;
    }
    
    /**
     * 检查表达式中是否已包含机构代码条件
     */
    private boolean hasOrgCodeCondition(Expression expression) {
        if (expression == null) {
            return false;
        }
        
        return containsOrgCodeColumn(expression);
    }
    
    /**
     * 递归检查表达式中是否包含机构代码列
     */
    private boolean containsOrgCodeColumn(Expression expression) {
        if (expression instanceof Column) {
            Column column = (Column) expression;
            return ORG_CODE_COLUMN.equalsIgnoreCase(column.getColumnName());
        } else if (expression instanceof BinaryExpression) {
            BinaryExpression binaryExpression = (BinaryExpression) expression;
            return containsOrgCodeColumn(binaryExpression.getLeftExpression()) ||
                   containsOrgCodeColumn(binaryExpression.getRightExpression());
        }
        
        return false;
    }
    
    /**
     * 判断是否需要添加机构代码条件
     */
    private boolean shouldAddOrgCodeCondition(String tableName) {
        if (isBlankString(tableName)) {
            return false;
        }
        
        String pureTableName = getPureTableName(tableName);
        
        // 检查排除列表
        if (EXCLUDE_TABLES.contains(pureTableName.toLowerCase())) {
            return false;
        }
        
        // 检查包含前缀
        return INCLUDE_TABLE_PREFIXES.stream()
                .anyMatch(prefix -> pureTableName.toLowerCase().startsWith(prefix.toLowerCase()));
    }
    
    /**
     * 获取纯表名（去除schema和引号）
     */
    private String getPureTableName(String tableName) {
        if (isBlankString(tableName)) {
            return tableName;
        }
        
        String pureTableName = tableName;
        
        // 去除schema前缀
        if (tableName.contains(".")) {
            String[] parts = tableName.split("\\.");
            pureTableName = parts[parts.length - 1];
        }
        
        // 去除引号
        if (pureTableName.startsWith("`") && pureTableName.endsWith("`")) {
            pureTableName = pureTableName.substring(1, pureTableName.length() - 1);
        }
        if (pureTableName.startsWith("\"") && pureTableName.endsWith("\"")) {
            pureTableName = pureTableName.substring(1, pureTableName.length() - 1);
        }
        
        return pureTableName;
    }
    
    /**
     * 应用SQL修改
     */
    private void applySqlModification(StatementHandler statementHandler, String modifiedSql) {
        try {
            Object boundSql = statementHandler.getBoundSql();
            
            // 使用反射修改SQL
            java.lang.reflect.Field sqlField = boundSql.getClass().getDeclaredField("sql");
            sqlField.setAccessible(true);
            sqlField.set(boundSql, modifiedSql);
            
            if (enableLogging) {
                log.debug("SQL修改成功");
            }
            
        } catch (Exception e) {
            log.error("应用SQL修改失败", e);
            if (enableStrictMode) {
                throw new RuntimeException("应用SQL修改失败", e);
            }
        }
    }
    
    /**
     * 检查字符串是否为空或空白
     */
    private boolean isBlankString(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }
    
    @Override
    public void setProperties(Properties properties) {
        if (properties != null) {
            String sqlModificationProp = properties.getProperty("enableSqlModification");
            if (sqlModificationProp != null) {
                this.enableSqlModification = Boolean.parseBoolean(sqlModificationProp);
            }
            
            String loggingProp = properties.getProperty("enableLogging");
            if (loggingProp != null) {
                this.enableLogging = Boolean.parseBoolean(loggingProp);
            }
            
            String strictModeProp = properties.getProperty("enableStrictMode");
            if (strictModeProp != null) {
                this.enableStrictMode = Boolean.parseBoolean(strictModeProp);
            }
            
            log.info("增强版多机构拦截器配置 - 启用SQL修改: {}, 启用日志: {}, 严格模式: {}", 
                    enableSqlModification, enableLogging, enableStrictMode);
        }
    }
}
