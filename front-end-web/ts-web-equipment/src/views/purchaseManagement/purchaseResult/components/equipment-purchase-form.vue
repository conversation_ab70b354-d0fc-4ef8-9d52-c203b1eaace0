<template>
  <ts-form ref="form" :model="form" labelWidth="140px">
    <div
      v-for="(logItem, parentIndex) in form.purchaseLogList"
      :key="parentIndex"
    >
      <ts-row class="info-title">
        采购信息-{{ parentIndex + 1 }}
        <div class="right-btn">
          <i
            class="add-btn el-icon-circle-plus"
            v-if="actionType == 'edit'"
            @click="handleAddList"
          ></i>
          <i
            class="remove-btn el-icon-remove"
            v-if="form.purchaseLogList.length != 1 && !logItem.id"
            @click="handleRemoveList(parentIndex)"
          ></i>
        </div>
      </ts-row>
      <ts-row>
        <ts-col :span="8">
          <ts-form-item
            label="采购项目名称"
            :prop="'purchaseLogList.' + parentIndex + '.name'"
            :rules="editRules.required"
          >
            <ts-input
              :key="parentIndex"
              v-model="form.purchaseLogList[parentIndex].name"
              placeholder="请输入"
              :disabled="actionType != 'edit'"
            />
          </ts-form-item>
        </ts-col>
        <ts-col :span="8">
          <ts-form-item
            :prop="'purchaseLogList.' + parentIndex + '.purchaseDate'"
            :rules="editRules.required"
            label="采购(合同)时间"
          >
            <ts-date-picker
              style="width:100%"
              v-model="logItem.purchaseDate"
              valueFormat="YYYY-MM-DD"
              placeholder="请选择"
              :disabled="actionType != 'edit'"
            />
          </ts-form-item>
        </ts-col>

        <ts-col :span="8">
          <ts-form-item
            label="供应商"
            :prop="'purchaseLogList.' + parentIndex + '.supplier'"
            :rules="editRules.required"
          >
            <ts-input
              v-model="logItem.supplier"
              placeholder="请输入"
              :disabled="actionType != 'edit'"
            />
          </ts-form-item>
        </ts-col>
      </ts-row>

      <ts-row>
        <ts-col :span="8">
          <ts-form-item
            label="采购方式"
            :prop="'purchaseLogList.' + parentIndex + '.purchaseWay'"
            :rules="editRules.required"
          >
            <ts-select
              style="width: 100%"
              v-model="logItem.purchaseWay"
              clearable
              placeholder="请选择"
              :disabled="actionType != 'edit'"
            >
              <ts-option
                v-for="item of purchaseType"
                :key="item.id"
                :label="item.itemName"
                :value="item.itemNameValue"
              ></ts-option>
            </ts-select>
          </ts-form-item>
        </ts-col>
        <ts-col :span="8">
          <ts-form-item
            label="生产厂商"
            :prop="'purchaseLogList.' + parentIndex + '.producer'"
            :rules="editRules.required"
          >
            <ts-input
              v-model="logItem.producer"
              placeholder="请输入"
              :disabled="actionType != 'edit'"
            />
          </ts-form-item>
        </ts-col>
        <ts-col :span="8">
          <ts-form-item
            label="品牌"
            :prop="'purchaseLogList.' + parentIndex + '.purchaseBrand'"
            :rules="editRules.required"
          >
            <ts-input
              v-model="logItem.purchaseBrand"
              placeholder="请输入"
              :disabled="actionType != 'edit'"
            />
          </ts-form-item>
        </ts-col>
      </ts-row>

      <ts-row>
        <ts-col :span="8">
          <ts-form-item
            label="规格型号"
            :prop="'purchaseLogList.' + parentIndex + '.purchaseSpec'"
            :rules="editRules.required"
          >
            <ts-input
              v-model="logItem.purchaseSpec"
              placeholder="请输入"
              :disabled="actionType != 'edit'"
            />
          </ts-form-item>
        </ts-col>
        <ts-col :span="8">
          <ts-form-item label="设备通用名">
            <ts-input
              v-model="logItem.commonName"
              :maxlength="20"
              placeholder="请输入"
              :disabled="actionType != 'edit'"
            />
          </ts-form-item>
        </ts-col>
        <ts-col :span="8">
          <ts-form-item
            label="设备分类"
            :prop="'purchaseLogList.' + parentIndex + '.cateId'"
            :rules="editRules.required"
          >
            <ts-ztree-select
              ref="deptSelect"
              defaultExpandAll
              :data="classificationData"
              :inpText.sync="logItem.cateName"
              :inpVal.sync="logItem.cateId"
              placeholder="请选择"
              :disabled="actionType != 'edit'"
            />
          </ts-form-item>
        </ts-col>
      </ts-row>
      <ts-row>
        <ts-col :span="8">
          <ts-form-item
            label="单价(万元)"
            :prop="'purchaseLogList.' + parentIndex + '.purchasePrice'"
            :rules="editRules.required"
          >
            <ts-input
              v-model="logItem.purchasePrice"
              placeholder="请输入"
              :disabled="actionType != 'edit'"
              @input="
                validateInputFourDecimal($event, parentIndex, 'purchasePrice')
              "
              @blur="
                multiplyCalculate(
                  [logItem.purchaseNumbers, logItem.purchasePrice],
                  parentIndex,
                  'purchaseTotalPrice'
                )
              "
            />
          </ts-form-item>
        </ts-col>
        <ts-col :span="8">
          <ts-form-item
            label="数量"
            :prop="'purchaseLogList.' + parentIndex + '.purchaseNumbers'"
            :rules="editRules.required"
          >
            <ts-input
              v-model="logItem.purchaseNumbers"
              placeholder="请输入"
              :disabled="
                actionType != 'edit' || (logItem.id && logItem.id != null)
              "
              @input="
                validateInputIntNum($event, parentIndex, 'purchaseNumbers')
              "
              @blur="
                multiplyCalculate(
                  [logItem.purchaseNumbers, logItem.purchasePrice],
                  parentIndex,
                  'purchaseTotalPrice'
                ) && changeDeviceList(logItem.purchaseNumbers, parentIndex)
              "
            />
          </ts-form-item>
        </ts-col>
        <ts-col :span="8">
          <ts-form-item
            label="金额(万元)"
            :prop="'purchaseLogList.' + parentIndex + '.purchaseTotalPrice'"
            :rules="editRules.required"
          >
            <ts-input
              v-model="logItem.purchaseTotalPrice"
              :disabled="actionType != 'edit'"
              :readonly="true"
              placeholder="请输入"
            />
          </ts-form-item>
        </ts-col>
      </ts-row>
      <ts-row>
        <ts-col :span="16">
          <ts-form-item
            label="采购合同"
            :prop="'purchaseLogList.' + parentIndex + '.purchaseFiles'"
            :rules="editRules.required"
          >
            <base-upload
              v-model="logItem.purchaseFiles"
              :onlyRead="actionType != 'edit'"
            ></base-upload>
          </ts-form-item>
        </ts-col>
      </ts-row>
      <ts-row>
        <form-table
          class="form-table"
          :formData="form.purchaseLogList[parentIndex]"
          :disabled="true"
          operateDataKey="deviceList"
          :columns="equipmentInfoColumns"
        >
          <template v-slot:deviceCode="{ row, column, index }">
            <ts-form-item
              :key="`${column.property + '_' + index}`"
              class="flex-item"
              label=""
              label-width="0"
              :prop="
                `purchaseLogList.${parentIndex}.deviceList.${index}.${column.property}`
              "
              :rules="editRules.required"
            >
              <span class="required-icon">*</span>
              <ts-input
                style="width: 100%"
                placeholder="请输入"
                :disabled="actionType != 'edit'"
                v-model="row[column.property]"
              />
            </ts-form-item>
          </template>
          <template v-slot:name="{ row, column, index }">
            <ts-form-item
              :key="`${column.property + '_' + index}`"
              class="flex-item"
              label=""
              label-width="0"
              :prop="
                `purchaseLogList.${parentIndex}.deviceList.${index}.${column.property}`
              "
              :rules="editRules.required"
            >
              <span class="required-icon">*</span>
              <ts-input
                style="width: 100%"
                placeholder="请输入"
                :disabled="actionType != 'edit'"
                v-model="row[column.property]"
              />
            </ts-form-item>
          </template>
          <template v-slot:assetCode="{ row, column }">
            <ts-input
              style="width: 100%"
              :disabled="actionType != 'edit'"
              placeholder="请输入"
              v-model="row[column.property]"
            />
          </template>
          <template v-slot:status="{ row, column, index }">
            <ts-form-item
              :key="`${column.property + '_' + index}`"
              class="flex-item"
              label=""
              label-width="0"
              :prop="
                `purchaseLogList.${parentIndex}.deviceList.${index}.${column.property}`
              "
              :rules="editRules.required"
            >
              <span class="required-icon">*</span>
              <ts-select
                style="width: 100%"
                :disabled="actionType != 'edit'"
                v-model="row[column.property]"
                clearable
                placeholder="请选择"
              >
                <ts-option
                  v-for="item of statusList"
                  :key="item.id"
                  :label="item.itemName"
                  :value="item.itemNameValue"
                ></ts-option>
              </ts-select>
            </ts-form-item>
          </template>
        </form-table>
      </ts-row>
    </div>
    <ts-row class="info-title">招标信息</ts-row>
    <ts-row>
      <ts-col :span="16">
        <ts-form-item
          label="招标参数"
          prop="tenderParameter"
          :rules="editRules.required"
        >
          <base-upload
            v-model="form.tenderParameter"
            :onlyRead="actionType != 'edit'"
          >
          </base-upload>
        </ts-form-item>
      </ts-col>
    </ts-row>
    <ts-row>
      <ts-col :span="16">
        <ts-form-item
          label="招投标备案资料"
          prop="tenderFiles"
          :rules="editRules.required"
        >
          <base-upload
            v-model="form.tenderFiles"
            :onlyRead="actionType != 'edit'"
          >
          </base-upload>
        </ts-form-item>
      </ts-col>
    </ts-row>
    <ts-row>
      <ts-col :span="16">
        <ts-form-item label="备注">
          <ts-input
            v-model="form.purchaseRemark"
            type="textarea"
            class="textarea"
            maxlength="500"
            placeholder="请输入"
            show-word-limit
            :disabled="actionType != 'edit'"
          />
        </ts-form-item>
      </ts-col>
    </ts-row>
  </ts-form>
</template>

<script>
import dialogForm from '../mixins/dialog-form';
import FormTable from '@/components/form-table.vue';
import { deepClone } from '@/util/index.js';
export default {
  name: 'equipmentPurchaseForm',
  mixins: [dialogForm],
  components: {
    FormTable
  },
  data() {
    return {
      classificationData: [],
      statusList: [],
      equipmentInfoColumns: [
        {
          prop: 'deviceCode',
          label: '设备编码',
          align: 'center'
        },
        // {
        //   prop: 'name',
        //   label: '设备名称',
        //   align: 'left'
        // },
        {
          prop: 'assetCode',
          label: '资产编码',
          align: 'center'
        }
        // {
        //   prop: 'status',
        //   label: '状态',
        //   align: 'center'
        // }
      ]
    };
  },
  created() {
    this.ajax.getDictionaries('EQUIPMENT_STSTUS', this, 'statusList');
    this.ajax.getEquipmentCategoryTree().then(res => {
      if (res.success == false || res.statusCode != 200) {
        this.$message.error(res.message || '设备分类获取失败');
        return;
      }
      this.classificationData = res.object;
    });
  },
  methods: {
    findInTree(tree, id) {
      if (tree.id === id) {
        return tree;
      }
      for (let i = 0; i < (tree.children || []).length; i++) {
        const found = findInTree(tree.children[i], value);
        if (found) {
          return found;
        }
      }
      return null;
    },
    changeDeviceList(number, index) {
      let keyObject = {};
      let formDataKey = this.equipmentInfoColumns.reduce((prev, cur) => {
        prev[cur.prop] = cur.defaultValue || '';
        return keyObject;
      }, keyObject);
      let formDataArr = Array.from({ length: number }, (_, i) => {
        formDataKey.name = this.form.purchaseLogList[index].name;
        return deepClone(formDataKey);
      });
      this.$set(this.form.purchaseLogList[index], 'deviceList', formDataArr);
    }
  }
};
</script>

<style lang="scss" scoped>
.info-title {
  border-left: 3px solid $primary-blue;
  font-weight: bold;
  font-size: 16px;
  padding-left: 8px;
  line-height: 1;
  margin: 16px 0;
  position: relative;
}
.right-btn {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
}
.add-btn {
  font-size: 24px;
  color: $primary-blue;
  margin-right: 8px;
  cursor: pointer;
}
.remove-btn {
  font-size: 24px;
  color: $disabled-color;
  margin-right: 8px;
  cursor: pointer;
}
.required-icon {
  color: #f56c6c;
  margin-right: 4px;
}
/deep/ {
  .flex-item .el-form-item__content {
    display: flex;
    align-items: center;
  }
  #SetFormTableForm .el-input {
    min-width: unset;
  }
  .el-input.el-input--prefix .el-input__inner {
    padding-left: 8px !important;
  }
}
</style>
