<template>
  <!-- 输入框 -->
  <div style="width: 50%">
    <ts-form-item :label="fieldDesc" :prop="fieldName" :rules="requiredValue">
      <ts-input
        v-model="formData[fieldName]"
        :maxlength="Number(fieldLength)"
        :placeholder="fieldTip"
        :readonly="readonlyValue"
        :disabled="disabled"
      />
    </ts-form-item>
  </div>
</template>

<script>
import props from './props';
import { dealBigMoney, toDecimal } from '@/util/index.js';
export default {
  mixins: [props],
  data() {
    return {
      disabled: false
    };
  },
  watch: {
    '$store.state.draft.contractAmount': {
      handler(val) {
        if (this.renderStatus === 'add') {
          const label = this.fieldDesc;
          switch (label) {
            case '合同金额':
              this.$set(this.formData, this.fieldName, toDecimal(val));
              break;
            case '金额大写':
              this.$set(this.formData, this.fieldName, dealBigMoney(val));
              break;
          }
        }
      },
      immediate: true
    }
  },
  created() {
    const label = this.fieldDesc;

    let disabledArr = ['合同类型', '金额大写', '合同金额'];
    this.disabled = disabledArr.includes(label);

    if (this.renderStatus === 'add') {
      switch (label) {
        case '合同类型':
          const contractTypeName = this.$attrs.typeContractData.definitionName;
          this.$set(this.formData, this.fieldName, contractTypeName);
          break;
      }
    }
  }
};
</script>
