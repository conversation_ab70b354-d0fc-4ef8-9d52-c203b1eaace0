import axios from 'axios';
import <PERSON> from 'raven-js';
import logs from '@trasen/trasen-logs/lib';
import ajax from '@/api/index.js';

export default {
  install(Vue) {
    Vue.use(ajax);
    Vue.use(logs, {
      Raven,
      axios,
      organization: '组织名称',
      webProject: '前端项目名称',
      version: '1.0.0', //项目版本号
      url: 'http://127.0.0.1:7001/saveLogs' //服务器日志接口地址
    }); //初始化日志
  }
};
