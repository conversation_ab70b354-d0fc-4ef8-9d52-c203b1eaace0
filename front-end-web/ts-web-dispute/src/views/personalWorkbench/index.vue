<template>
  <div class="trasen-container flex-column">
    <ts-tabs v-model="activeTable" @tab-click="refresh">
      <ts-tab-pane name="0">
        <span slot="label">待办 ({{ tabList[0] }}) </span>
      </ts-tab-pane>
      <ts-tab-pane name="1">
        <span slot="label">进行中 ({{ tabList[1] }}) </span>
      </ts-tab-pane>
      <ts-tab-pane name="2">
        <span slot="label">已办结 ({{ tabList[2] }}) </span>
      </ts-tab-pane>
    </ts-tabs>
    <AllTable
      ref="allTable"
      :tableData.sync="tableDatas[activeTable]"
      :activeTable="activeTable"
      @event="handleEvent"
      @refresh="handleGetTableData"
      @export="handleExport"
    />

    <ts-dialog :title="title" :visible.sync="showEditModal" :fullscreen="true">
      <div v-if="showEditModal" class="dispute-action-content flex-column">
        <ts-tabs
          v-model="editActiveTab"
          :type="null"
          :class="{
            'register-add-borad': [
              'report',
              'registerAdd',
              'registerEdit'
            ].includes(editType),
            'action-tab flex-column ts-tabs tabs-content': true
          }"
        >
          <ts-tab-pane label="基本信息" name="basic">
            <el-scrollbar
              ref="basicScroll"
              style="height: 100%;"
              wrap-style="height: calc(100% + 17px);"
            >
              <DisputeBaseInformForm
                ref="baseForm"
                v-if="
                  ['report', 'registerAdd', 'registerEdit'].includes(editType)
                "
                :data="baseInfo"
                :renderType="editType"
              />
              <DisputeBaseInformBoard ref="baseForm" v-else :data="baseInfo" />
            </el-scrollbar>
          </ts-tab-pane>
          <!-- <ts-tab-pane label="附件" name="files">
            <UploadDetail :data="baseInfo.operationLogs" />
          </ts-tab-pane> -->
          <ts-tab-pane label="操作日志" name="logs">
            <LogDetail :data="baseInfo.operationLogs" />
          </ts-tab-pane>
        </ts-tabs>
        <DisputeActionBoard
          ref="form"
          v-if="showDisputeAction"
          v-model="editData"
          :editType="editType"
          :baseInfo="baseInfo"
        />
      </div>
      <div slot="footer">
        <template v-if="editType == 'registerAdd'">
          <!-- 办结 -->
          <ts-button
            v-if="editData.isSolved == 2"
            @click="handleResigtAndFinished"
          >
            保存
          </ts-button>
          <!-- 分发 -->
          <ts-button v-else @click="handleRegistAndDispute">
            保存
          </ts-button>
        </template>
        <ts-button v-if="editType == 'report'" @click="handleReport(1)">
          存草稿
        </ts-button>
        <ts-button
          v-if="editType != 'showDisputeDetial'"
          type="primary"
          @click="handleSubmit"
        >
          {{ title }}
        </ts-button>
        <ts-button @click="handleCancelModal">取消</ts-button>
      </div>
    </ts-dialog>
  </div>
</template>

<script>
import allTable from './components/all-table.vue';
import disputeBaseInformForm from '@/components/dispute-base-inform-form/index.vue';
import indexJs from './index.js';
import logDetail from '@/views/disputeCenter/components/log-detial.vue';
import disputeActionBoard from '@/views/disputeCenter/components/dispute-action-board.vue';
import disputeBaseInformBoard from '@/components/dispute-base-inform-board/index.vue';

export default {
  mixins: [indexJs],
  components: {
    AllTable: allTable,
    DisputeBaseInformForm: disputeBaseInformForm,
    LogDetail: logDetail,
    DisputeActionBoard: disputeActionBoard,
    DisputeBaseInformBoard: disputeBaseInformBoard
  },
  data() {
    return {
      activeTable: '0',
      tabList: [],

      tableDatas: {},

      deptTreeData: []
    };
  },
  computed: {
    showDisputeAction() {
      return ![
        'editType',
        'showDisputeDetial',
        'registerEdit',
        'report'
      ].includes(this.editType);
    }
  },
  watch: {
    'editData.isSolved': function() {
      this.$nextTick(() => {
        this.$refs.basicScroll.update();
      });
    }
  },
  methods: {
    async refresh() {
      this.$refs.allTable.refresh();
    },
    /**@desc 获取 头部tab数据 */
    async getDisputeTypeNums() {
      let res = await this.ajax.getPersonalWorkbenchTabData();
      if (!res.success) {
        this.$message.error(res.message || '分类数据获取失败');
        return;
      }
      let { dbCount = 0, jxzCount = 0, ybjCount = 0 } = res.object || {};
      let newStatusList = [dbCount, jxzCount, ybjCount];
      this.tabList = newStatusList;
    },
    /**@desc 刷新编辑所需要用到的数据 */
    refreshEditDatas() {
      this.getDeptTreeData();
    },
    getDeptTreeData() {
      this.ajax.getDeptTree().then(res => {
        if (!res.success) {
          this.$message.error(res.message || '科室数据获取失败');
          return;
        }
        this.deptTreeData = res.object;
      });
    },
    async getPeopleListData(data) {
      let res = await this.ajax.getEmployeeByPage({ ...data, pageSize: 15 });

      if (res.success == false) {
        this.$message.error(res.message || '人员数据获取失败');
        return false;
      }
      return res.rows;
    }
  }
};
</script>

<style lang="scss" scoped>
.tabs-content {
  /deep/ .el-tabs__content {
    flex: 1;
    overflow: hidden;
  }
  /deep/ .el-tab-pane {
    height: 100%;
    overflow: hidden;
  }
}
/deep/ .table-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
/deep/ .form-table {
  flex: 1;
}
/deep/ .pagination-content {
  margin-top: $primary-spacing;
  text-align: right;
}

/deep/.dispute-action-content {
  height: calc(100vh - 143px);
  overflow: hidden;
  .el-input {
    min-width: unset;
  }
}
.action-tab {
  flex: 1;
  overflow: hidden;
}
/deep/ .register-add-borad .el-tabs__header {
  height: 0;
  overflow: hidden;
}
</style>
