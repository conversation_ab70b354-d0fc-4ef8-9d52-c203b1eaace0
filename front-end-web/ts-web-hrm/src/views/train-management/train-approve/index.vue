<template>
  <div class="trasen-container flex-column">
    <ts-tabs v-model="activeTab" @tab-click="refresh">
      <ts-tab-pane name="1">
        <span slot="label">待审批</span>
      </ts-tab-pane>
      <ts-tab-pane name="2">
        <span slot="label">已审批</span>
      </ts-tab-pane>
      <ts-tab-pane name="3">
        <span slot="label">全部</span>
      </ts-tab-pane>
    </ts-tabs>

    <ts-search-bar
      v-model="searchForm[activeTab]"
      :actions="actions"
      :formList="searchList"
      :elementCol="14"
      @search="refresh"
      :class="{
        'broken-search-bar': activeTab == 1
      }"
    >
      <!-- <template slot="applicantDeptName">
        <ts-ztree-select
          :inpText.sync="searchForm[activeTab].applicantDeptName"
          :inpVal.sync="searchForm[activeTab].applicantDeptCode"
          :data="deptTreeData"
          defaultExpandAll
        ></ts-ztree-select>
      </template> -->
      <!-- <template slot="applicantName">
        <base-select
          v-model="searchForm[activeTab].applicantCode"
          :inputText.sync="searchForm[activeTab].applicantName"
          :loadMethod="getPeopleListData"
          label="employeeName"
          value="employeeNo"
          searchInputName="employeeName"
        ></base-select>
      </template> -->
      <!-- <template slot="canceledEmpName">
        <base-select
          v-model="searchForm[activeTab].canceledEmpCode"
          :inputText.sync="searchForm[activeTab].canceledEmpName"
          :loadMethod="getPeopleListData"
          label="employeeName"
          value="employeeNo"
          searchInputName="employeeName"
        ></base-select>
      </template> -->
    </ts-search-bar>

    <base-table
      ref="table"
      border
      stripe
      v-loading="loading"
      @refresh="handleRefreshTable"
    >
      <ts-table-column
        v-for="item of columns"
        :key="item.label"
        v-bind="item"
      ></ts-table-column>
      <template v-if="activeTab != 1">
        <ts-table-column
          label="审批意见"
          prop="planState"
          minWidth="180"
          align="center"
          :formatter="computedPlanState"
        ></ts-table-column>
        <ts-table-column label="审批人" prop="canceledEmp"></ts-table-column>
        <ts-table-column
          label="审批时间"
          prop="canceledDate"
          minWidth="180"
          align="center"
        ></ts-table-column>
      </template>
      <ts-table-column label="申请人" prop="applicantName"></ts-table-column>
      <ts-table-column
        label="申请人科室"
        prop="applicantDeptName"
        minWidth="120"
      ></ts-table-column>
      <ts-table-column
        label="申请时间"
        prop="createDate"
        minWidth="180"
        align="center"
      ></ts-table-column>
      <ts-table-column
        v-if="activeTab == 1"
        label="操作"
        fixed="right"
        :formatter="computeActionCell"
      ></ts-table-column>
    </base-table>

    <ts-dialog
      :title="showType == 'preview' ? '详情' : '审批'"
      :visible.sync="modalVisible"
      type="large"
    >
      <div class="dialog-content">
        <el-scrollbar style="flex: 1;">
          <ts-form v-show="showType == 'approval'" ref="form" :model="editData">
            <ts-form-item
              label="审批结果"
              prop="approvalResult"
              :rules="requiredRow"
            >
              <ts-radio-group v-model="editData.approvalResult">
                <ts-radio :label="2">同意</ts-radio>
                <ts-radio :label="3">不同意</ts-radio>
              </ts-radio-group>
            </ts-form-item>
            <ts-form-item label="备注">
              <ts-input
                v-model="editData.approvalComment"
                type="textarea"
                rows="4"
                resize="none"
                maxlength="300"
                show-word-limit
              ></ts-input>
            </ts-form-item>
          </ts-form>
          <preview-plan-tab
            v-if="showType == 'preview'"
            ref="preview"
            :isAdmin="true"
          />
        </el-scrollbar>
      </div>
      <template slot="footer">
        <ts-button
          v-show="showType == 'approval'"
          type="primary"
          @click="handleConfirm"
        >
          确定
        </ts-button>
        <ts-button @click="handleCloseModal">
          {{ showType != 'preview' ? '取消' : '关闭' }}
        </ts-button>
      </template>
    </ts-dialog>
  </div>
</template>

<script>
import { TrainingTypeList } from '../data.js';
import { deepClone } from '@/unit/commonHandle.js';
import previewPlanTab from '../components/preview-content.vue';
export default {
  components: {
    previewPlanTab
  },
  data() {
    return {
      activeTab: '1',

      loading: false,
      searchForm: {
        1: {},
        2: {},
        3: {}
      },
      actions: [
        {
          label: '导出',
          click: this.handleExport
        }
      ],
      searchList: [
        {
          label: '培训主题',
          value: 'trainTopic',
          element: 'ts-input',
          elementProp: {
            clearable: true,
            placeholder: '请输入培训主题'
          }
        },
        {
          label: '申请科室',
          value: 'applicantDeptName',
          element: 'ts-input',
          elementProp: {
            clearable: true,
            placeholder: '请输入申请科室'
          }
        },
        {
          label: '培训时间',
          value: 'trainingTimeList',
          element: 'ts-range-picker',
          elementProp: {
            format: 'YYYY-MM-DD',
            valueFormat: 'YYYY-MM-DD'
          }
        },
        {
          label: '培训类型',
          value: 'trainType',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: []
        },
        {
          label: '申请人',
          value: 'applicantName',
          element: 'ts-input',
          elementProp: {
            clearable: true,
            placeholder: '请输入申请人姓名'
          }
        },
        {
          label: '申请时间',
          value: 'createDateList',
          element: 'ts-range-picker',
          elementProp: {
            format: 'YYYY-MM-DD',
            valueFormat: 'YYYY-MM-DD',
            clearable: true
          }
        },
        {
          label: '审批人',
          value: 'canceledEmpName',
          element: 'ts-input',
          elementProp: {
            clearable: true,
            placeholder: '请输入审批人姓名'
          }
        },
        {
          label: '审批时间',
          value: 'approvalTimeList',
          element: 'ts-range-picker',
          elementProp: {
            format: 'YYYY-MM-DD',
            valueFormat: 'YYYY-MM-DD',
            clearable: true
          }
        },
        {
          label: '审批意见',
          value: 'approvalComment',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: [
            {
              label: '同意',
              value: '2',
              element: 'ts-option'
            },
            {
              label: '不同意',
              value: '3',
              element: 'ts-option'
            }
          ]
        }
      ],
      trainType: [], // 培训类型
      deptTreeData: [], // 科室树数据

      columns: [
        {
          label: '序号',
          prop: 'pageIndex',
          width: 60
        },
        {
          label: '培训主题',
          prop: 'trainTopic',
          minWidth: 220,
          formatter: (row, prop, cell) => {
            return (
              <span
                class="action-cell"
                onClick={() => this.handleShowDetail(row)}>
                {cell}
              </span>
            );
          }
        },
        {
          label: '培训时间',
          prop: 'trainStartTime',
          minWidth: 320,
          align: 'center',
          formatter: row => {
            let { trainStartTime, trainEndTime } = row,
              timeList = [trainStartTime, trainEndTime].filter(item => item);
            return timeList.join(' - ');
          }
        },
        {
          label: '培训讲师',
          prop: 'trainLecturer'
        },
        {
          label: '培训类型',
          prop: 'trainType',
          formatter: (row, prop, cell) => {
            return this.trainType.find(item => item.value == cell)?.label;
          }
        },
        {
          label: '培训人数',
          prop: 'trainNumbers',
          align: 'right'
        },
        // {
        //   label: '状态',
        //   prop: 'planState',
        //   align: 'center',
        //   formatter: (row, prop, cell) => {
        //     return TrainingTypeList.find(item => item.value == cell)?.label;
        //   }
        // },
        {
          label: '学时',
          prop: 'creditHour',
          align: 'right'
        },
        {
          label: '课程类型',
          prop: 'placeCourseType',
          width: 80,
          align: 'center'
        },
        {
          label: '培训层级',
          prop: 'placeLevel',
          width: 80,
          align: 'center'
        },
        {
          label: '学分',
          prop: 'credit',
          align: 'right'
        }
      ],

      modalVisible: false,
      showType: 'approval',
      editData: {},
      requiredRow: { required: true, message: '必填' }
    };
  },
  methods: {
    refresh() {
      this.$refs.table.pageNo = 1;
      this.$refs.table.triggerRefresh();
      this.getTrainingType();
      this.getDeptTreeData();
    },
    getSearchData() {
      let searchForm = this.searchForm[this.activeTab] || {},
        [startDate, endDate] = searchForm.trainingTimeList || [],
        [searchStartCreateDate, searchEndCreateDate] =
          searchForm.createDateList || [],
        [canceledStartDate, canceledEndDate] =
          searchForm.approvalTimeList || [],
        data = {
          auditType: this.activeTab,
          ...searchForm
        },
        assignData = {
          startDate,
          endDate,
          searchStartCreateDate,
          searchEndCreateDate
        };

      this.activeTab > 1 &&
        Object.assign(assignData, { canceledStartDate, canceledEndDate });
      Object.keys(assignData).map(key => {
        assignData[key] && (data[key] = assignData[key]);
      });
      delete data.trainingTimeList;
      delete data.createDateList;
      delete data.approvalTimeList;

      return data;
    },
    handleExport() {
      let data = this.getSearchData(),
        search = Object.keys(data)
          .map(key => {
            return `${key}=${data[key]}`;
          })
          .join('&'),
        aDom = document.createElement('a');
      aDom.href = '/ts-hrms/train/plan/export?' + search;
      aDom.click();
    },
    handleRefreshTable(page) {
      let { pageNo = 1, pageSize = 100 } = page || {},
        data = this.getSearchData();
      Object.assign(data, {
        pageNo,
        pageSize
      });

      this.ajax.getMyTrainPlanApprovalDataList(data).then(res => {
        let rows = res.rows.map((item, index) => {
          return {
            ...item,
            pageIndex: index + 1 + (pageNo - 1) * pageSize
          };
        });
        this.$refs.table.refresh({
          ...res,
          rows
        });
      });
    },
    /**@desc 获取培训类型 */
    getTrainingType() {
      this.ajax.getDataByDataLibrary('TRAINING_TYPE').then(res => {
        if (!res.success) {
          this.$message.error(res.message || '培训类型获取失败');
          return;
        }
        this.trainType = res.object.map(item => ({
          label: item.itemName,
          value: item.itemNameValue
        }));
        let index = this.searchList.findIndex(
          item => item.value == 'trainType'
        );
        if (index >= 0) {
          this.$set(
            this.searchList[index],
            'childNodeList',
            this.trainType.map(item => ({ ...item, element: 'ts-option' }))
          );
        }
      });
    },
    /**@desc 获取科室树数据 */
    async getDeptTreeData() {
      this.ajax.organizationZTreeList().then(res => {
        if (!res.success) {
          this.$message.error(res.message || '科室树数据获取失败');
          return;
        }
        this.deptTreeData = res.object;
      });
    },
    /**@desc 获取人员列表 */
    async getPeopleListData(data) {
      let res = await this.ajax.getEmployeeDataList({ ...data, pageSize: 15 });

      if (res.success == false) {
        this.$message.error(res.message || '人员数据获取失败');
        return false;
      }
      return res.rows;
    },
    computeActionCell(row) {
      let actionsList = [
        {
          label: '审批',
          event: 'approval'
        }
      ];
      return (
        <BaseActionCell
          actions={actionsList}
          on={{ 'action-select': e => this.handleShowApprovalRow(row) }}
        />
      );
    },
    computedPlanState(row, prop, cell) {
      let status =
        {
          2: {
            class: 'success-line',
            label: '通过'
          },
          3: {
            class: 'error-line',
            label: '不通过'
          }
        }[cell] || {};
      return <span class={status.class}>{status.label}</span>;
    },
    handleShowApprovalRow(row) {
      this.editData = deepClone(row);
      delete this.editData.approvalResult;
      delete this.editData.approvalComment;
      this.modalVisible = true;
      this.showType = 'approval';
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    async handleConfirm() {
      let validate = await this.$refs.form.validate().catch(res => res);
      if (!validate) {
        return;
      }
      let data = { ...this.editData };
      data.planState = data.approvalResult;
      delete data.approvalResult;

      this.ajax.handleEditTrainPlanData(data).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '操作失败');
          return;
        }
        this.handleCloseModal();
        this.$message.success('审批成功');
        this.refresh();
      });
    },
    handleCloseModal() {
      this.modalVisible = false;
      this.editData = {};
    },
    handleShowDetail(row) {
      this.ajax.getTrainPlanDetailData(row.trainPlanId).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '计划详情获取失败');
          return;
        }
        this.showType = 'preview';
        this.modalVisible = true;
        this.$nextTick(() => {
          this.$refs.preview &&
            this.$refs.preview.init({
              data: res.object,
              trainType: this.trainType
            });
        });
      });
    }
  },
  mounted() {
    {
      /* this.$EventBus.$on('mainMessage', (event, newVal = {}) => {
      let { path, businessId } = newVal;
      if (!event == 'messageToastEvent' || !path.includes(this.$route.path)) {
        return;
      }
      this.$nextTick(() => {
        this.handleShowDetail({ trainPlanId: businessId });
      });
    }); */
    }

    this.$event.create('mainMessage').listen('messageToastEvent', newVal => {
      let { path, businessId } = newVal;
      if (!path.includes(this.$route.path)) {
        return;
      }
      this.$nextTick(() => {
        this.handleShowDetail({ trainPlanId: businessId });
      });
    });
  }
};
</script>

<style lang="scss" scoped>
/deep/ .broken-search-bar .hidden-search-content .search-list-content {
  .search-list-item:nth-last-child(1),
  .search-list-item:nth-last-child(2),
  .search-list-item:nth-last-child(3) {
    display: none;
  }
}
.error-line {
  color: $error-color;
}
.success-line {
  color: $success-color;
}
.action-cell {
  color: $primary-blue;
  cursor: pointer;
}

.dialog-content {
  max-height: calc(100vh - 140px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
</style>
