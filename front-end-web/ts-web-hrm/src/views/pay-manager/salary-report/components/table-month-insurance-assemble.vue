<template>
  <div class="table-month-salary-summary">
    <div class="trasen-container">
      <ul class="search-type-tabs">
        <li
          :class="{
            'search-item': 'true',
            active: item.value == searchForm.reportId
          }"
          v-for="(item, index) in optionsArr"
          @click="handleClickSearchTabs(item)"
          :key="index"
        >
          {{ item.label }}
        </li>
      </ul>

      <ts-search-bar
        v-model="searchForm"
        :formList="searchList"
        :resetData="resetData"
        :elementCol="14"
        @search="search"
      >
        <template slot="date">
          <el-date-picker
            v-model="searchForm.date"
            type="monthrange"
            range-separator="至"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            value-format="yyyy-MM"
            @change="
              e => {
                this.$set(this.searchForm, 'date', e);
                this.search();
              }
            "
            :clearable="false"
          />
        </template>
        <template slot="age">
          <ts-number-range v-model="searchForm.age" type="decimal" />
        </template>
        <template slot="deptTree">
          <ts-ztree-select
            placeholder="请选择部门"
            ref="treeSelect"
            :inpText.sync="searchForm.orgName"
            :inpVal.sync="searchForm.code"
            :data="treeData"
            defaultExpandAll
            @change="handleChangeTree"
            @before-change="handleTreeBeforeChange"
          />
        </template>
        <template slot="right">
          <ts-button type="primary" @click="handleExport">
            导 出
          </ts-button>
        </template>
      </ts-search-bar>

      <!-- <base-table
        ref="table"
        class="form-table"
        border
        stripe
        :columns="columns"
        @refresh="handleRefreshTable"
      /> -->
      <TsVxeTemplateTable
        id="table-month-insurance-assemble"
        class="form-table"
        ref="table"
        :hasPage="false"
        :columns="columns"
        @refresh="handleRefreshTable"
        :span-method="colspanMethod"
      />
    </div>
  </div>
</template>

<script>
import searchList from '../mixins/searchList';
import { deepClone } from '@/unit/commonHandle.js';

export default {
  components: {},
  mixins: [searchList],
  props: {
    treeData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      operationkeys: ['reportId'],
      searchList: [
        {
          label: '',
          value: 'employeeName',
          element: 'ts-input',
          elementProp: {
            placeholder: '工号/姓名',
            clearable: true
          },
          event: {
            change: e => {
              this.$set(this.searchForm, 'employeeName', e);
              this.search();
            }
          }
        },
        {
          label: '算薪周期',
          value: 'date'
        },
        {
          label: '薪酬方案组',
          value: 'optionIds',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            multiple: true
          },
          childNodeList: []
        },
        {
          label: '部门',
          value: 'deptTree'
        }
      ],
      optionsArr: [],
      columns: [],
      tableData: [],
      personInformation: [],
      salaryItem: []
    };
  },
  methods: {
    colspanMethod({ _rowIndex, _columnIndex }) {
      if (_rowIndex === this.tableData.length - 1) {
        if (_columnIndex === 0) {
          return { rowspan: 0, colspan: 0 };
        } else if (_columnIndex === 1) {
          return { rowspan: 1, colspan: 7 };
        } else if (
          _columnIndex === 2 ||
          _columnIndex === 3 ||
          _columnIndex === 4 ||
          _columnIndex === 5 ||
          _columnIndex === 6
        ) {
          return { rowspan: 0, colspan: 0 };
        }
      }
    },
    handleClickSearchTabs(item) {
      this.$refs.table.pageNo = 1;
      this.searchForm.reportId = item.value;
      this.resetData.reportId = item.value;
      this.handleRefreshTable();
    },

    // 获取薪酬方案
    async handleGetNewSalaryOptionAllList() {
      let res = await this.ajax.newSalaryOptionAllList();
      if (res.success == false) {
        this.$message.error(res.message || '薪酬组数据获取失败');
        return;
      }
      let options = (res.object || []).map(m => {
        return {
          label: m.optionName,
          value: m.id,
          element: 'ts-option'
        };
      });
      let index = this.searchList.findIndex(f => f.value == 'optionIds');
      this.searchList[index].childNodeList = options;
    },

    handleChangeTree(e) {
      const { id, name } = e;
      this.searchForm.code = id;
      this.searchForm.orgName = name;
    },

    handleTreeBeforeChange(e, treeObj, fn) {
      return true;
    },

    refresh() {
      this.$nextTick(() => {
        this.handleRefreshTable();
      });
    },

    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    async handleRefreshTable() {
      await this.handleGetReportsList();
      await this.handleGetNewSalaryOptionAllList();
      this.handleGetTableHead();
      this.handleGetTableData();
    },

    async handleGetReportsList() {
      const res = await this.ajax.reportsList({
        sidx: 'create_date',
        sord: 'desc',
        reportsType: '3',
        pageNo: 1,
        pageSize: 999
      });
      let data = deepClone(res.rows || []);
      this.optionsArr = data.map(m => {
        return {
          label: m.reportName,
          value: m.id
        };
      });

      if (this.optionsArr && this.optionsArr.length) {
        if (!this.searchForm.reportId) {
          this.$set(this.searchForm, 'reportId', this.optionsArr[0].value);
        }
      }
    },

    //导出
    handleExport() {
      let resultForm = deepClone(this.handleGetSearchFromData());
      let pageNo = 1,
        pageSize = 9999999,
        formData = {
          pageNo,
          pageSize,
          ...resultForm
        };

      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      iframe.id = 'down-file-iframe';

      const form = document.createElement('form');
      form.method = 'get';
      form.target = iframe.id;
      form.action =
        '/ts-hrms/api/newsalaryReportsStatistics/salaryInsuranceExport';

      Object.keys(formData).map(key => {
        if (
          formData[key] == null ||
          formData[key] == undefined ||
          formData[key] == ''
        ) {
          delete formData[key];
        } else {
          const input = document.createElement('input');
          input.type = 'hidden';
          input.name = key;
          input.value = formData[key];
          form.appendChild(input);
        }
      });

      iframe.appendChild(form);
      document.body.appendChild(iframe);

      form.submit();
      document.body.removeChild(iframe);

      this.exportLoading = true;
      setTimeout(() => {
        this.exportLoading = false;
      }, 1500);
    },

    async handleGetTableHead() {
      this.columns = [
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 70,
          fixed: 'left'
        }
      ];
      let res = await this.ajax.salaryInsuranceTotalTitle(
        this.searchForm.reportId
      );
      if (res.success == false) {
        this.$message.error(res.message || '表头数据获取失败');
        return;
      }
      if (res.object.length) {
        let rows = res.object.map((item, i) => {
          let isFlex = i < 6 ? 'left' : '';
          return {
            fixed: isFlex,
            ...item
          };
        });
        this.columns.push(...rows);
      }
    },

    async handleGetTableData() {
      let resultForm = deepClone(this.handleGetSearchFromData());
      let pageNo = 1,
        pageSize = 9999999,
        searchForm = {
          pageNo,
          pageSize,
          ...resultForm
        };

      let res = await this.ajax.salaryInsuranceStatisticsData(searchForm);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.tableData = deepClone(rows);
      this.$refs.table.refresh({
        ...res,
        rows
      });

      let pageDom = document.querySelectorAll('.table-month-salary-summary');
      if (pageDom && pageDom.length) {
        let paginationDom = pageDom[0].querySelectorAll('.el-pagination');
        if (paginationDom[0]) paginationDom[0].style = 'visibility: hidden;';
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.table-month-salary-summary {
  height: calc(100% - 44px);
  display: flex;
  flex-direction: column;
  .filterate {
    height: 45px;
  }
  .trasen-container {
    padding-top: 0px !important;
    position: relative;
    overflow: hidden;

    flex: 1;
    display: flex;
    flex-direction: column;

    .search-type-tabs {
      display: flex;
      margin-bottom: 8px;
      .search-item {
        cursor: pointer;
        margin-right: 8px;
        border-radius: 4px;
        border: 2px solid #e1e1e1;
        padding: 8px 12px;
        font-weight: 800;
        line-height: 32px;
        height: 32px;
        &.active {
          color: $primary-blue;
          border: 2px solid $primary-blue;
        }
      }
    }

    .footer_total {
      position: absolute;
      left: 8px;
      bottom: 8px;
      z-index: 9999;
    }

    ::v-deep {
      .form-table {
        flex: 1;
        overflow: hidden;
        transform: scale(1);
        .details-span {
          color: $primary-blue;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
