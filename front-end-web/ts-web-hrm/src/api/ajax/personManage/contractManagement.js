import { request } from '@/api/ajax';
import { service } from '@/api/config';

export default {
  // 组织机构
  getOrganizationTree(data) {
    return request({
      url: service.tsBasics() + '/organization/getTree',
      method: 'post',
      data
    });
  },

  // 到期提醒数量
  getBubble(data) {
    return request({
      url: '/ts-hrms/contract/getBubble',
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  },

  // 员工合同列表
  getEmployeeDataList(data) {
    return request({
      url: '/ts-hrms/contract/getEmployeeDataList',
      method: 'post',
      data
    });
  },

  // 签订记录
  contractGetDataList(data) {
    return request({
      url: '/ts-hrms/contract/getDataList',
      method: 'post',
      data
    });
  },

  // 到期提醒
  getRemindDataList(data) {
    return request({
      url: '/ts-hrms/contract/getRemindDataList',
      method: 'post',
      data
    });
  },

  // 操作记录
  getContractLog(data) {
    return request({
      url: '/ts-hrms/contract/getContractLog',
      method: 'post',
      data
    });
  },

  // 操作类型
  getContractOperationStatusList(data) {
    return request({
      url: '/ts-hrms/enum/getContractOperationStatusList',
      method: 'post',
      data
    });
  },

  // 签订合同
  contractSave(data) {
    return request({
      url: '/ts-hrms/contract/save',
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  },

  // 编辑合同
  contractUpdate(data) {
    return request({
      url: '/ts-hrms/contract/update',
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  },

  // 获取个人对应的合同类型的合同数据
  getEmployeeOldContract(data) {
    return request({
      url: '/ts-hrms/contract/getEmployeeOldContract',
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  },

  // 获取到期合同数量
  getBubble(data) {
    return request({
      url: '/ts-hrms/contract/getBubble',
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  },

  // 获取到期合同列表
  getRemindDataList(data) {
    return request({
      url: '/ts-hrms/contract/getRemindDataList',
      method: 'post',
      data
    });
  },

  // 合同状态
  getContractStatusList() {
    return request({
      url: '/ts-hrms/enum/getContractStatusList',
      method: 'post',
      data: {
        pageNo: 1,
        pageSize: 999,
        select: 1
      }
    });
  },

  // 获取个人信息
  findByEmployeeId(id) {
    return request({
      url: `/ts-basics-bottom/employee/findByEmployeeId/${id}`,
      method: 'post'
    });
  },

  // 获取合同信息列表
  getAllByEmployeeId(id) {
    return request({
      url: `/ts-hrms/contract/getAllByEmployeeId/${id}`,
      method: 'post'
    });
  },

  // 续签合同
  contractRelieve(data) {
    return request({
      url: `/ts-hrms/contract/relieve`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  },

  // 删除合同
  contractRemove(data) {
    return request({
      url: '/ts-hrms/contract/remove',
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  },

  // 续签合同
  contractRenew(data) {
    return request({
      url: '/ts-hrms/contract/renew',
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  },

  // 解除合同
  contractRelieve(data) {
    return request({
      url: '/ts-hrms/contract/relieve',
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  }
};
