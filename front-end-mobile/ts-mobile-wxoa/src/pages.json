{"easycom": {"^u-(.*)": "@trasen-oa/trasen-uview-ui/components/u-$1/u-$1.vue"}, "pages": [{"path": "pages/patrol/patrol-type-list/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/patrol/patrol-reporting/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/patrol/patrol-management-list/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/patrol/patrol-detail/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/political/political-type-list/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/political/political-reporting/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/political/political-management-list/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/political/political-detail/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/choose-person/choose-person", "style": {"navigationStyle": "custom"}}, {"path": "pages/choose-dept/choose-dept", "style": {"navigationStyle": "custom"}}, {"path": "pages/shift-view/index", "style": {"navigationStyle": "custom"}}], "globalStyle": {"navigationStyle": "custom", "navigationBarTextStyle": "black", "navigationBarBackgroundColor": "#FFFFFF", "backgroundColor": "#f7f8fa"}}