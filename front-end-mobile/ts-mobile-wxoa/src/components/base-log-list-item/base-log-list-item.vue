<template>
  <view class="log-list-item" :style="itemStyle">
    <slot></slot>
  </view>
</template>

<script>
export default {
  name: 'base-log-list-item',
  props: {
    itemStyle: {
      type: String,
      default: ''
    }
  }
};
</script>

<style lang="scss" scoped>
.log-list-item {
  position: relative;
  padding-left: $uni-spacing-row-lg;
  &::before {
    content: '';
    position: absolute;
    top: 8px;
    left: 0;
    -webkit-transform: translateX(-8px);
    transform: translateX(-8px);
    width: 14px;
    height: 14px;
    border-radius: 100%;
    background: $uni-color-primary;
  }
}
</style>
