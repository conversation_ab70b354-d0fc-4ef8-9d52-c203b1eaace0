<template>
  <view class="upload-file">
    <view class="upload-file__header">
      <view class="upload-file__header--left">
        {{ label }}
      </view>
      <view
        v-if="maxCount > lists.length && !disabledUpload"
        class="upload-file__header--right add-icon-box"
        hover-class="u-add-wrap__hover"
        hover-stay-time="150"
        @click="selectFile"
      >
        <slot name="addBtn"></slot>
        <u-icon
          v-if="!customBtn"
          class="add-icon"
          :name="addIcon"
          :color="addIconColor"
          :size="addIconSize"
        ></u-icon>
      </view>
    </view>
    <u-upload
      max-count="9"
      :width="imageWidth"
      :height="imageHeight"
      :deletable="deletable"
      :disabled="disabled"
      :del-icon="imageDelIcon"
      :del-bg-color="imageDelBgColor"
      :action="action"
      :index="index"
      :show-progress="showProgress"
      :disabled-upload="true"
      :file-list="formImageLists"
      :imageMode="imageMode"
      @on-remove="removeFile"
    ></u-upload>
    <view class="upload-file__list" :style="listStyles">
      <view
        class="file__list-item-box"
        v-for="(item, i) in formFileLists"
        :key="i"
        :style="i !== 0 && listItemStyles"
      >
        <view class="file__list-item">
          <view class="files__list-item__name" :style="listItemStyles">
            {{ item.name }}
          </view>
          <view
            v-if="item.error"
            class="retry-icon-box"
            @click.stop="retryItem(index, item)"
          >
            <u-icon
              class="retry-icon"
              :name="retryIcon"
              :color="retryIconColor"
              :size="retryIconSize"
            ></u-icon>
          </view>
          <view
            v-if="deletable"
            class="del-icon-box"
            @click.stop="delItem(index, item)"
          >
            <u-icon
              class="del-icon"
              :name="delIcon"
              :color="delIconColor"
              :size="delIconSize"
            ></u-icon>
          </view>
        </view>
        <u-line-progress
          v-if="
            showProgress &&
              item.progress > 0 &&
              item.progress != 100 &&
              !item.error
          "
          :show-percent="false"
          height="12"
          class="files__list-item__progress"
          :percent="item.progress"
        ></u-line-progress>
      </view>
    </view>
  </view>
</template>

<script>
/**
 * upload-file 文件上传
 * @description 该组件用于表单中上传文件场景
 * @property {String} action 服务器上传地址
 * @property {String Number} max-count 最大选择图片的数量（默认99）
 * @property {Boolean} custom-btn 如果需要自定义选择图片的按钮，设置为true（默认false）
 * @property {Boolean} show-progress 是否显示进度条（默认true）
 * @property {Boolean} disabled-upload 是否禁止上传文件（默认false）
 * @property {String} del-icon 右上角删除图标名称
 * @property {String | Number} index 在各个回调事件中的最后一个参数返回，用于区别是哪一个组件的事件
 * @property {String} del-color 右上角关闭按钮图标的颜色
 * @property {Object} header 上传携带的头信息，对象形式
 * @property {Object} form-data 上传额外携带的参数
 * @property {String} name 上传文件的字段名，供后端获取使用（默认file）
 * @property {String}	mediatype = [image|video|all]	选择文件类型
 * 	@value image	只选择图片
 * 	@value video	只选择视频
 * 	@value all		选择所有文件
 * @property {Array<String>} extension 选择文件后缀，在微信环境中，如果mediatype="all"，extension属性会失效
 * @property {Array<String>} source-type 选择图片的来源（仅在mediatype为image或video时可用），album-从相册选图，camera-使用相机，默认二者都有（默认['album', 'camera']）
 * @property {Boolean} preview-full-image	是否可以通过uni.previewImage预览已选择的图片（默认true）
 * @property {Boolean} multiple	是否开启图片多选，部分安卓机型不支持（默认true）
 * @property {Boolean} readonly 是否只读，即不显示删除按钮（默认false）
 * @property {String Number} max-size 选择单个文件的最大大小，单位B(byte)，默认不限制（默认Number.MAX_VALUE）
 * @property {Array<Object>} file-list 默认显示的图片列表，数组元素为对象，必须提供url属性
 * @property {Boolean} auto-upload 选择完图片是否自动上传，见上方说明（默认true）
 * @property {Boolean} show-tips 特殊情况下是否自动提示toast，见上方说明（默认true）
 * @property {Boolean} show-upload-list 是否显示组件内部的图片预览（默认true）
 * @event {Function} on-oversize 文件大小超出最大允许大小
 * @event {Function} on-preview 全屏预览图片时触发
 * @event {Function} on-remove 移除文件时触发
 * @event {Function} on-success 文件上传成功时触发
 * @event {Function} on-change 文件上传后，无论成功或者失败都会触发
 * @event {Function} on-error v上传失败时触发
 * @event {Function} on-progress 文件上传过程中的进度变化过程触发
 * @event {Function} on-uploaded 所有文件上传完毕触发
 * @event {Function} on-choose-complete 每次选择文件后触发，只是让外部可以得知每次选择后，内部的文件列表
 */
export default {
  name: 'base-upload-file',
  props: {
    //上传选择图标左侧的label文字
    label: {
      type: String,
      default: '附件'
    },
    //上传选择图标名称，只能为uview内置图标
    addIcon: {
      type: String,
      default: 'plus-circle'
    },
    //上传选择图标的颜色
    addIconColor: {
      type: String,
      default: '#666666'
    },
    //上传选择图标的大小
    addIconSize: {
      type: [String, Number],
      default: 40
    },
    // 是否通过slot自定义传入选择图标的按钮
    customBtn: {
      type: Boolean,
      default: false
    },
    // 后端地址
    action: {
      type: String,
      default: ''
    },
    // 最大上传数量
    maxCount: {
      type: [String, Number],
      default: 99
    },
    //  是否显示进度条
    showProgress: {
      type: Boolean,
      default: true
    },
    // 是否启用
    disabled: {
      type: Boolean,
      default: false
    },
    // 预览上传的图片时的裁剪模式，和image组件mode属性一致
    imageMode: {
      type: String,
      default: 'aspectFill'
    },
    // 头部信息
    header: {
      type: Object,
      default() {
        return {};
      }
    },
    // 额外携带的参数
    formData: {
      type: Object,
      default() {
        return {};
      }
    },
    // 上传的文件字段名
    name: {
      type: String,
      default: 'file'
    },
    // 所选的图片的尺寸, 可选值为original compressed
    sizeType: {
      type: Array,
      default() {
        return ['original', 'compressed'];
      }
    },
    sourceType: {
      type: Array,
      default() {
        return ['album', 'camera'];
      }
    },
    // 是否在点击预览图后展示全屏图片预览
    previewFullImage: {
      type: Boolean,
      default: true
    },
    // 是否开启图片多选，部分安卓机型不支持
    multiple: {
      type: Boolean,
      default: true
    },
    // 是否展示删除按钮
    deletable: {
      type: Boolean,
      default: true
    },
    // 文件大小限制，单位为byte
    maxSize: {
      type: [String, Number],
      default: Number.MAX_VALUE
    },
    // 显示已上传的文件列表
    fileList: {
      type: Array,
      default() {
        return [];
      }
    },
    // 上传区域的提示文字
    uploadText: {
      type: String,
      default: '选择图片'
    },
    // 是否自动上传
    autoUpload: {
      type: Boolean,
      default: true
    },
    // 是否显示toast消息提示
    showTips: {
      type: Boolean,
      default: true
    },
    // 是否隐藏上传图标的按钮
    disabledUpload: {
      type: Boolean,
      default: false
    },
    // 内部预览图片区域和选择图片按钮的区域宽度
    imageWidth: {
      type: [String, Number],
      default: 160
    },
    // 内部预览图片区域和选择图片按钮的区域高度
    imageHeight: {
      type: [String, Number],
      default: 160
    },
    // 图片右上角删除图标名称，只能为uView内置图标
    imageDelIcon: {
      type: String,
      default: 'close'
    },
    // 图片右上角关闭按钮的背景颜色
    imageDelBgColor: {
      type: String,
      default: '#fa3534'
    },
    // 删除图标名称，只能为uView内置图标
    delIcon: {
      type: String,
      default: 'close'
    },
    //删除图标的颜色
    delIconColor: {
      type: String,
      default: '#666666'
    },
    //删除图标的大小
    delIconSize: {
      type: [String, Number],
      default: 14
    },
    //重新上传图标名称，只能为uView内置图标
    retryIcon: {
      type: String,
      default: 'reload'
    },
    //重新上传图标的颜色
    retryIconColor: {
      type: String,
      default: '#666666'
    },
    //重新上传图标的大小
    retryIconSize: {
      type: [String, Number],
      default: 14
    },
    // 如果上传后的返回值为json字符串，是否自动转json
    toJson: {
      type: Boolean,
      default: true
    },
    // 上传前的钩子，每个文件上传前都会执行
    beforeUpload: {
      type: Function,
      default: null
    },
    // 移除文件前的钩子
    beforeRemove: {
      type: Function,
      default: null
    },
    listStyles: {
      type: Object,
      default() {
        return {
          // 是否显示边框
          border: false,
          // 是否显示分隔线
          dividline: true,
          // 线条样式
          borderStyle: {}
        };
      }
    },
    listItemStyles: {
      type: Object,
      default() {
        return {
          // 是否显示边框
          border: false,
          // 是否显示分隔线
          dividline: true,
          // 线条样式
          borderStyle: {}
        };
      }
    },
    // 允许上传的图片后缀
    extension: {
      type: Array,
      default() {
        // 支付宝小程序真机选择图片的后缀为"image"
        // https://opendocs.alipay.com/mini/api/media-image
        return [
          'jpeg',
          'jpg',
          'gif',
          'bmp',
          'png',
          'txt',
          'ppt',
          'pptx',
          'xls',
          'xlsx',
          'doc',
          'docx',
          'pdf',
          'wav',
          'zip',
          '7z',
          'rar',
          'image'
        ];
      }
    },
    // 在各个回调事件中的最后一个参数返回，用于区别是哪一个组件的事件
    index: {
      type: [Number, String],
      default: ''
    }
  },
  data() {
    return {
      lists: [],
      imageExtension: ['gif', 'png', 'jpg', 'jpeg', 'webp', 'bmp', 'image'],
      formImageLists: [],
      formFileLists: [],
      isInCount: true,
      uploading: false
    };
  },
  watch: {
    fileList: {
      immediate: true,
      handler(val) {
        val.map(value => {
          // 首先检查内部是否已经添加过此文件，因为外部绑定了一个对象给fileList的话(对象引用)，进行修改外部fileList
          // 时，会触发watch，导致重新把原来的图片再次添加到this.lists
          // 数组的some方法意思是，只要数组元素有任意一个元素条件符合，就返回true，而另一个数组的every方法的意思是数组所有元素都符合条件才返回true
          let tmp = this.lists.some(val => {
            return val.url == value.url;
          });
          // 如果内部没有这个文件(tmp为false)，则添加到内部
          if (!tmp) {
            this.lists.push({ url: value.url, error: false, progress: 100 });
            //先获取文件后缀，判断文件类型，gif|jpg|jpeg|png|webp|bmp|image类型添加到图片列表，其他类型添加到文件列表
            let flie = {
                name: value.name,
                url: value.url,
                error: false,
                progress: 100
              },
              fileExt = this.getFileExt(value.fkFileName),
              isImg = this.checkImageFileExt(fileExt);
            if (isImg) {
              this.formImageLists.push(flie);
            } else {
              this.formFileLists.push(flie);
            }
          }
        });
      }
    },
    // 监听lists的变化，发出事件
    lists(n) {
      this.$emit('on-list-change', n, this.index);
    }
  },
  computed: {
    styles() {
      let styles = {
        border: true,
        dividline: true,
        'border-style': {}
      };
      return Object.assign(styles, this.listStyles);
    },
    borderStyle() {
      let { borderStyle, border } = this.styles;
      let obj = {};
      if (!border) {
        obj.border = 'none';
      } else {
        let width = (borderStyle && borderStyle.width) || 1;
        width = this.value2px(width);
        let radius = (borderStyle && borderStyle.radius) || 5;
        radius = this.value2px(radius);
        obj = {
          'border-width': width,
          'border-style': (borderStyle && borderStyle.style) || 'solid',
          'border-color': (borderStyle && borderStyle.color) || '#eee',
          'border-radius': radius
        };
      }
      let classles = '';
      for (let i in obj) {
        classles += `${i}:${obj[i]};`;
      }
      return classles;
    },
    borderLineStyle() {
      let obj = {};
      let { borderStyle } = this.styles;
      if (borderStyle && borderStyle.color) {
        obj['border-color'] = borderStyle.color;
      }
      if (borderStyle && borderStyle.width) {
        let width = (borderStyle && borderStyle.width) || 1;
        let style = (borderStyle && borderStyle.style) || 0;
        if (typeof width === 'number') {
          width += 'px';
        } else {
          width = width.indexOf('px') ? width : width + 'px';
        }
        obj['border-width'] = width;

        if (typeof style === 'number') {
          style += 'px';
        } else {
          style = style.indexOf('px') ? style : style + 'px';
        }
        obj['border-top-style'] = style;
      }
      let classles = '';
      for (let i in obj) {
        classles += `${i}:${obj[i]};`;
      }
      return classles;
    }
  },
  methods: {
    // 清除列表
    clear() {
      this.lists = [];
      this.formImageLists = [];
      this.formFileLists = [];
    },
    // 重新上传队列中上传失败的所有文件
    reUpload() {
      this.uploadFile();
    },
    // 选择图片
    selectFile() {
      if (this.disabled) return;
      const {
        name = '',
        maxCount,
        multiple,
        maxSize,
        lists,
        formFileLists,
        formImageLists,
        extension
      } = this;
      let chooseFile = null;
      const newMaxCount = maxCount - lists.length;
      // 设置为只选择图片的时候使用 chooseImage 来实现
      chooseFile = new Promise((resolve, reject) => {
        // #ifdef H5
        uni.chooseFile({
          count: multiple ? (newMaxCount > 9 ? 9 : newMaxCount) : 1,
          success: resolve,
          extension,
          fail: reject
        });
        // #endif
        // #ifdef MP-WEIXIN
        wx.chooseMessageFile({
          count: multiple ? (newMaxCount > 9 ? 9 : newMaxCount) : 1,
          extension,
          success(res) {
            const tempFilePaths = res.tempFiles;
            resolve(tempFilePaths);
          }
        });
        // #endif
      });
      chooseFile
        .then(res => {
          let listOldLength = this.lists.length;
          res.tempFiles.map((val, index) => {
            // 检查文件后缀是否允许，如果不在this.extension内，就会返回false
            if (!this.checkFileExt(val)) return;

            // 如果是非多选，index大于等于1或者超出最大限制数量时，不处理
            if (!multiple && index >= 1) return;
            if (val.size > maxSize) {
              this.$emit('on-oversize', val, this.lists, this.index);
              this.showToast('超出允许的文件大小');
            } else {
              if (maxCount <= lists.length) {
                this.$emit('on-exceed', val, this.lists, this.index);
                this.showToast('超出最大允许的文件个数');
                return;
              }
              let file = {
                  name: val.name,
                  url: val.path,
                  error: false,
                  progress: 0,
                  file: val
                },
                fileExt = this.getFileExt(val.name),
                isImg = this.checkImageFileExt(fileExt);
              if (isImg) {
                formImageLists.push(file);
              } else {
                formFileLists.push(file);
              }
              lists.push(file);
            }
          });
          // 每次文件选择完，抛出一个事件，并将当前内部选择的文件数组抛出去
          this.$emit('on-choose-complete', this.lists, this.index);
          if (this.autoUpload) this.uploadFile(listOldLength);
        })
        .catch(error => {
          this.$emit('on-choose-fail', error);
        });
    },
    // 提示用户消息
    showToast(message, force = false) {
      if (this.showTips || force) {
        uni.showToast({
          title: message,
          icon: 'none'
        });
      }
    },
    // 该方法供用户通过ref调用，手动上传
    upload() {
      this.uploadFile();
    },
    // 对失败的文件重新上传
    retry(itemIndex, item) {
      this.formFileLists[itemIndex].progress = 0;
      this.formFileLists[itemIndex].error = false;
      this.formFileLists[itemIndex].response = null;
      let index = this.list.findIndex(i => {
        return i.url == item.url;
      });
      uni.showLoading({
        title: '重新上传'
      });
      this.uploadFile(index, itemIndex);
    },
    // 上传文件
    async uploadFile(index = 0, itemIndex = 0) {
      if (this.disabled) return;
      if (this.uploading) return;
      // 全部上传完成
      if (index >= this.lists.length) {
        this.$emit('on-uploaded', this.lists, this.index);
        return;
      }
      // 检查是否是已上传或者正在上传中
      if (this.lists[index].progress == 100) {
        if (this.autoUpload == false) this.uploadFile(index + 1);
        return;
      }
      // 执行before-upload钩子
      if (this.beforeUpload && typeof this.beforeUpload === 'function') {
        // 执行回调，同时传入索引和文件列表当作参数
        // 在微信，支付宝等环境(H5正常)，会导致父组件定义的customBack()函数体中的this变成子组件的this
        // 通过bind()方法，绑定父组件的this，让this.customBack()的this为父组件的上下文
        // 因为upload组件可能会被嵌套在其他组件内，比如u-form，这时this.$parent其实为u-form的this，
        // 非页面的this，所以这里需要往上历遍，一直寻找到最顶端的$parent，这里用了this.$u.$parent.call(this)
        // 明白意思即可，无需纠结this.$u.$parent.call(this)的细节
        let beforeResponse = this.beforeUpload.bind(this.$u.$parent.call(this))(
          index,
          this.lists
        );
        // 判断是否返回了promise
        if (!!beforeResponse && typeof beforeResponse.then === 'function') {
          await beforeResponse
            .then(res => {
              // promise返回成功，不进行动作，继续上传
            })
            .catch(err => {
              // 进入catch回调的话，继续下一张
              return this.uploadFile(index + 1);
            });
        } else if (beforeResponse === false) {
          // 如果返回false，继续下一张图片的上传
          return this.uploadFile(index + 1);
        } else {
          // 此处为返回"true"的情形，这里不写代码，就跳过此处，继续执行当前的上传逻辑
        }
      }
      // 检查上传地址
      if (!this.action) {
        this.showToast('请配置上传地址', true);
        return;
      }
      this.lists[index].error = false;
      let fileType = this.checkImageFileExt(this.lists[index].name)
        ? 'formImageLists'
        : 'formFileLists';
      this.uploading = true;
      // 创建上传对象
      const task = uni.uploadFile({
        url: this.action,
        filePath: this.lists[index].url,
        name: this.name,
        formData: this.formData,
        header: this.header,
        success: res => {
          // 判断是否json字符串，将其转为json格式
          let data =
            this.toJson && this.$u.test.jsonString(res.data)
              ? JSON.parse(res.data)
              : res.data;
          if (![200, 201, 204].includes(res.statusCode)) {
            this.uploadError(index, itemIndex, fileType, data);
          } else {
            // 上传成功
            this.lists[index].response = data;
            this.lists[index].progress = 100;
            this.lists[index].error = false;
            this[fileType][itemIndex].response = data;
            this[fileType][itemIndex].progress = 100;
            this[fileType][itemIndex].error = false;
            this.$emit('on-success', data, index, this.lists, this.index);
          }
        },
        fail: e => {
          this.uploadError(index, itemIndex, fileType, e);
        },
        complete: res => {
          uni.hideLoading();
          this.uploading = false;
          this.uploadFile(index + 1);
          this.$emit('on-change', res, index, this.lists, this.index);
        }
      });
      task.onProgressUpdate(res => {
        if (res.progress > 0) {
          this.lists[index].progress = res.progress;
          this[fileType][itemIndex].progress = 100;
          this.$emit('on-progress', res, index, this.lists, this.index);
        }
      });
    },
    // 上传失败
    uploadError(index, itemIndex, fileType, err) {
      this.lists[index].progress = 0;
      this.lists[index].error = true;
      this.lists[index].response = null;
      this[fileType][itemIndex].progress = 0;
      this[fileType][itemIndex].error = true;
      this[fileType][itemIndex].response = null;
      this.$emit('on-error', err, index, this.lists, this.index);
      this.showToast('上传失败，请重试');
    },
    // 删除一个文件
    deleteItem(index, itemIndex) {
      uni.showModal({
        title: '提示',
        content: '您确定要删除此项吗？',
        success: async res => {
          if (res.confirm) {
            // 先检查是否有定义before-remove移除前钩子
            // 执行before-remove钩子
            if (this.beforeRemove && typeof this.beforeRemove === 'function') {
              // 此处钩子执行 原理同before-remove参数，见上方注释
              let beforeResponse = this.beforeRemove.bind(
                this.$u.$parent.call(this)
              )(index, this.lists);
              // 判断是否返回了promise
              if (
                !!beforeResponse &&
                typeof beforeResponse.then === 'function'
              ) {
                await beforeResponse
                  .then(res => {
                    // promise返回成功，不进行动作，继续上传
                    this.handlerDeleteItem(index);
                  })
                  .catch(err => {
                    // 如果进入promise的reject，终止删除操作
                    this.showToast('已终止移除');
                  });
              } else if (beforeResponse === false) {
                // 返回false，终止删除
                this.showToast('已终止移除');
              } else {
                // 如果返回true，执行删除操作
                this.handlerDeleteItem(index);
              }
            } else {
              // 如果不存在before-remove钩子，
              this.handlerDeleteItem(index);
            }
          }
        }
      });
    },
    // 执行移除图片的动作，上方代码只是判断是否可以移除
    handlerDeleteItem(index) {
      // 如果文件正在上传中，终止上传任务，进度在0 < progress < 100则意味着正在上传
      if (this.lists[index].process < 100 && this.lists[index].process > 0) {
        typeof this.lists[index].uploadTask != 'undefined' &&
          this.lists[index].uploadTask.abort();
      }
      this.lists.splice(index, 1);
      this.$forceUpdate();
      this.$emit('on-remove', index, this.lists, this.index);
      this.showToast('移除成功');
    },
    // 用户通过ref手动的形式，移除一张图片
    remove(index) {
      // 判断索引的合法范围
      if (index >= 0 && index < this.lists.length) {
        this.lists.splice(index, 1);
        this.$emit('on-list-change', this.lists, this.index);
      }
    },
    // 预览图片
    doPreviewImage(url, index) {
      if (!this.previewFullImage) return;
      const images = this.lists.map(item => item.url || item.path);
      uni.previewImage({
        urls: images,
        current: url,
        success: () => {
          this.$emit('on-preview', url, this.lists, this.index);
        },
        fail: () => {
          uni.showToast({
            title: '预览图片失败',
            icon: 'none'
          });
        }
      });
    },
    //判断文件是否为图片格式
    checkImageFileExt(fileExt) {
      const reg = /gif|png|jpg|jpeg|webp|bmp|image/i;
      return reg.test(fileExt);
    },
    //获取文件后缀
    getFileExt(path) {
      const reg = /.+\./;
      return path.replace(reg, '').toLowerCase();
    },
    // 判断文件后缀是否允许
    checkFileExt(file) {
      // 检查是否在允许的后缀中
      let noArrowExt = false;
      // 获取后缀名
      let fileExt = '';
      // 如果是H5，需要从name中判断
      // #ifdef H5
      fileExt = this.getFileExt(file.name);
      // #endif
      // 非H5，需要从path中读取后缀
      // #ifndef H5
      fileExt = this.getFileExt(file.path);
      // #endif
      // 使用数组的some方法，只要符合extension中的一个，就返回true
      noArrowExt = this.extension.some(ext => {
        // 转为小写
        return ext.toLowerCase() === fileExt;
      });
      if (!noArrowExt) this.showToast(`不允许选择${fileExt}格式的文件`);
      return noArrowExt;
    },
    value2px(value) {
      if (typeof value === 'number') {
        value += 'px';
      } else {
        value = value.indexOf('px') !== -1 ? value : value + 'px';
      }
      return value;
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../../assets/css/flex.scss';
@import '../../assets/css/ellipsis.scss';
.upload-file {
  width: 100%;
  @include vue-flex(column);
  &__header {
    width: 100%;
    @include vue-flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    &--right {
      @include vue-flex;
      justify-content: flex-end;
      align-items: center;
    }
  }
  .file__list-item {
    width: 100%;
    @include vue-flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    &__name {
      flex: 1;
      @include ellipsis;
    }
  }
}
</style>
