const modulesFiles = require.context('./api', true, /\.js$/);
const modules = modulesFiles.keys().reduce((modules, modulePath) => {
  const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1');
  modules[moduleName] = modulesFiles(modulePath).default;
  return modules;
}, {});

export default {
  install(Vue) {
    let ajax = {};
    Object.keys(modules).forEach(key => {
      Object.keys(modules[key]).forEach(_key => {
        ajax[_key] = modules[key][_key];
      });
    });
    Vue.prototype.ajax = ajax;
  }
};
