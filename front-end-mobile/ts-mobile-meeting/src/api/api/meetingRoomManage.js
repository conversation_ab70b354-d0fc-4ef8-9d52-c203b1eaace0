import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 【字典管理】根据字典类型查询字典列表
   * @param {Object} data 数据对象**/
  getDictDatas(params) {
    return request.get(`${apiConfig.basics()}/dictItem/getDictItemByTypeCode`, {
      params
    });
  },
  /**@desc 会议室列表**/
  getBoardRoomList(data) {
    return request.post(`${apiConfig.oa()}/boardRoom/getList`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  // 获取列表
  getBoardRoomMeetingList(data) {
    return request.post(`${apiConfig.oa()}/boardRoomMeeting/getList`, data);
  },
  /**@desc 获取设备列表**/
  getDeviceList() {
    return request.get(`${apiConfig.oa()}/boardRoomDevice/getList`, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 获取楼栋/位置**/
  getLocationList() {
    return request.get(`${apiConfig.oa()}/boardRoom/getLocationList`, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 删除设备**/
  deleteDevice(data) {
    return request.post(`${apiConfig.oa()}/boardRoomDevice/del`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 新增设备**/
  addBoardRoomDevice(data) {
    return request.post(`${apiConfig.oa()}/boardRoomDevice/add`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc  获取应用基本设置**/
  getPersonListFullPathPost(api, method = 'GET', params) {
    return request.request({
      method: method.toUpperCase(), // 请求方法必须大写
      url: api || `${apiConfig.oa()}/employee/getEmployeeList`,
      data: params
    });
  },
  getDeptList(api, param) {
    return request.post(api, param);
  },
  getPersonByDept(data) {
    return request.post(
      `${apiConfig.basics()}/employee/getEmployeeAllPageList`,
      data,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  // 新增会议室
  addBoardRoom(data) {
    return request.post(`${apiConfig.oa()}/boardRoom/add`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  // 修改会议室
  editBoardRoom(data) {
    return request.post(`${apiConfig.oa()}/boardRoom/edit`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  // 删除会议室
  delBoardRoom(data) {
    return request.post(`${apiConfig.oa()}/boardRoom/del`, data);
  },
  // 获取会议室详情
  getBoardRoom(data) {
    return request.post(`${apiConfig.oa()}/boardRoom/get`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },

  // 禁用会议室
  disableBoardRoom(data) {
    return request.post(`${apiConfig.oa()}/boardRoom/disable`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  // 取消禁用
  enableBoardRoom(data) {
    return request.post(`${apiConfig.oa()}/boardRoom/enable`, data);
  },
  // 会议室预定时间轴详情列表
  getBoardRoomApplyTimeDetailList(data) {
    return request.post(
      `${apiConfig.oa()}/boardRoomApply/getBoardRoomApplyTimeDetailList`,
      data,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  // 获取工作流列表
  getListWorkflowList(data) {
    return request.post(
      `${apiConfig.oa()}/boardRoomApply/getListWorkflowList`,
      data
    );
  },
  // 获取预定详情
  getBoardRoomApply(data) {
    return request.post(`${apiConfig.oa()}/boardRoomApply/get`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },

  // 获取附件
  getFileAttachmentByBusinessId(params) {
    return request.get(
      `${apiConfig.basics()}/fileAttachment/getFileAttachmentByBusinessId`,
      {
        params: params,
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  // 删除附件
  deleteFileId(params) {
    return request.get(`${apiConfig.basics()}/fileAttachment/deleteFileId`, {
      params: params,
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  // 会议审批撤销
  cancelBoardRoomApply(data) {
    return request.post(`${apiConfig.oa()}/boardRoomApply/cancel`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  // 议程保存
  updateBoardRoomAgenda(data) {
    return request.post(`${apiConfig.oa()}/boardRoomAgenda/get`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  // 不通过
  failBoardRoomApply(data) {
    return request.post(`${apiConfig.oa()}/boardRoomApply/fail`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  // 通过
  passBoardRoomApply(data) {
    return request.post(`${apiConfig.oa()}/boardRoomApply/pass`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  // 根据任务ID获取下一审批节点
  getNextWfStepListByTaskId(data) {
    return request.post(
      `/ts-workflow/workflow/task/getNextWfStepListByTaskId`,
      data
    );
  },
  // 完成任务-根据节点ID生成待办任务
  completeTaskByWfStepId(data) {
    return request.post(
      `/ts-workflow/workflow/task/completeTaskByWfStepId`,
      data
    );
  },
  // 获取会议纪要列表
  getListBoardRoomSummary(data) {
    return request.post(`${apiConfig.oa()}/boardRoomSummary/getList`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  // 会议室预定
  addBoardRoomApply(data) {
    return request.post(`${apiConfig.oa()}/boardRoomApply/add`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  // 获取列表
  getListBoardRoomSubscribe(data) {
    return request.post(`${apiConfig.oa()}/boardRoomSubscribe/getList`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  // 订阅
  addBoardRoomSubscribe(data) {
    return request.post(`${apiConfig.oa()}/boardRoomSubscribe/add`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  // 取消订阅
  delBoardRoomSubscribe(data) {
    return request.post(`${apiConfig.oa()}/boardRoomSubscribe/del`, data);
  },
  // 获取统计
  countBoardRoomSummary() {
    return request.get(`${apiConfig.oa()}/boardRoomSummary/count`);
  },
  // 一键处理过期申请
  batchExpire() {
    return request.post(`${apiConfig.oa()}/boardRoomApply/batchExpire`);
  },
  // 获取签到详情列表
  getBoardRoomSignInList(data) {
    return request.post(`${apiConfig.oa()}/boardRoomSignIn/getList`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  // 获取会议详情
  getBoardRoomMeeting(data) {
    return request.post(`${apiConfig.oa()}/boardRoomMeeting/get`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  // 取消会议
  cancelBoardRoomMeeting(data) {
    return request.post(`${apiConfig.oa()}/boardRoomMeeting/cancel`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  // 结束会议
  endBoardRoomApply(data) {
    return request.post(`${apiConfig.oa()}/boardRoomMeeting/end`, data);
  },
  // 获取会议详情
  editBoardRoomApply(data) {
    return request.post(`${apiConfig.oa()}/boardRoomApply/modify`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 请假 */
  handleLeaveMeeting(data) {
    return request.post(`${apiConfig.oa()}/boardRoomSignIn/leave`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  // 签到统计
  getSignInCount(params) {
    return request.get(`${apiConfig.oa()}/boardRoomSignIn/getCount`, {
      params
    });
  },
  // 签到
  signInReq(data) {
    return request.post(`${apiConfig.oa()}/boardRoomSignIn/signInReq`, data);
  },
  // 签退
  signOut(data) {
    return request.post(`${apiConfig.oa()}/boardRoomSignIn/signOutReq`, data);
  },
  // 复制businessId相关的文件
  copyBusinessIdFiles(params) {
    return request.get(
      `${apiConfig.basics()}/fileAttachment/copyBusinessIdFiles`,
      {
        params
      }
    );
  },
  /**@desc 获取人员列表**/
  getEmployeeList(datas) {
    return request.get(`${apiConfig.oa()}/employee/getEmployeeList`, {
      params: datas,
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  //获取用户信息
  getUserInfo() {
    return request.get(`${apiConfig.basics()}/employee/getMyEmployeeDetail`);
  },
  /**@desc 获取群组**/
  getOrgGroupList(datas) {
    return request.get(
      `${apiConfig.basics()}/employee/orgGroup/getOrgGroupList`,
      {
        params: datas,
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        },
        custom: {
          showLoading: true,
          loadingText: '加载中...'
        }
      }
    );
  },

  //获取用系统信息
  getAllGlobalSetting() {
    return request.get(
      `${apiConfig.basics()}/globalSetting/getAllGlobalSetting`
    );
  }
};
