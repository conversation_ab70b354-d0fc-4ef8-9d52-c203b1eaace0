import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 人员选择控件 从通讯录选择-获取联系人分页接口
   * @param {Object} data 数据对象**/
  getContactDatas(page, data = {}) {
    return request.post(
      `${apiConfig.basics()}/employee/pageSelect?pageNo=${
        page.pageNo
      }&pageSize=${page.pageSize}`,
      data,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },

  getMyEmployeeDetail() {
    return request.get(`${apiConfig.basics()}/employee/getMyEmployeeDetail`);
  },
  /**@desc 人员选择控件 从通讯录选择-获取系统群组、个人群组 分组分页接口
   * @param {Object} object 数据对象**/
  getEmployeeOrgGroupList(object) {
    return request.post(
      `${apiConfig.basics()}/employee/orgGroup/list`,
      {
        pageSize: object.pageSize,
        pageNo: object.pageNo,
        groupType: object.groupType
      },
      {
        header: {
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
        }
      }
    );
  },
  /**@desc 人员选择控件 从通讯录选择-获取系统群组、个人群组 分组人员分页接口
   * @param {String} groupId 数据对象**/
  async getListPageOrgGroupEmp(data) {
    const res = await request.post(
      `${apiConfig.basics()}/employee/orgGroup/listPageOrgGroupEmp`,
      {
        groupId: data.groupId,
        pageSize: 9999,
        pageNo: 1
      },
      {
        header: {
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
        }
      }
    );
    const { id, name, type } = data.dataKey;

    res.rows.forEach(item => {
      item[id] = item.empId;
      item[name] = item.empName;
      item[type] = 1;
      item.avatar = (item.avatar && item.avatar + '/80x80.png') || null;
    });

    return res;
  },
  /**@desc 人员选择控件 从通讯录选择-获取外部联系人 分页接口
   * @param {Object} object 数据对象**/
  getEmployeeLinkman(object) {
    return request.post(
      `${apiConfig.basics()}/employee/linkman/list`,
      {
        pageSize: object.pageSize,
        pageNo: object.pageNo
      },
      {
        header: {
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
        }
      }
    );
  },
  getTree() {
    return request.post(`${apiConfig.basics()}/organization/getTree`);
  },
  /**@desc 第二版人员选择控件 获取机构与机构下的人员
   * @param {Object} data 数据对象**/
  async getOrgEmp(data = {}) {
    const res = await request.post(
      `${apiConfig.basics()}/organization/getOrgEmp`,
      {
        name: data.name,
        parentId: data.parentId
      },
      {
        custom: {
          showLoading: true,
          loadingText: '稍等'
        },
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
    if (res.success && res.statusCode === 200) {
      const { id, name, type } = data.dataKey;

      res.object.empList.forEach(item => {
        item[id] = item.empId;
        item[name] = item.empName;
        item[type] = 1;

        item.avatar = (item.avatar && item.avatar + '/80x80.png') || null;
      });

      res.object.orgList.forEach(item => {
        item[id] = item.orgId;
        item[name] = item.orgName;
        item[type] = 2;
      });

      return res;
    }
  }
};
