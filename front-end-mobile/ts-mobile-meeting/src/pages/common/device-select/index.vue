<template>
  <view class="device_select">
    <u-popup
      v-model="myValue"
      mode="bottom"
      :safe-area-inset-bottom="true"
      @open="open"
      @close="close"
    >
      <view class="device_select_container">
        <view class="u-select__header">
          <view
            @click="handleCancel"
            class="u-select__header__confirm u-select__header__btn"
          >
            取消
          </view>
          <view class="u-select__header__title">设备</view>
          <view
            @click="handleOk"
            style="color: #005bac;"
            class="u-select__header__confirm u-select__header__btn"
          >
            确定
          </view>
        </view>
        <scroll-view :scroll-y="true" style="height: 720rpx;">
          <view class="device_select_content">
            <view class="content">
              <view
                class=""
                v-for="(deviceItem, idx) in deviceList"
                :key="idx"
                @click="handleChange(deviceItem)"
                :class="[
                  'device_item',
                  ' mr_8',
                  ' mb_12',
                  selectKeys.some(e => {
                    return e.id === deviceItem.id;
                  })
                    ? 'active'
                    : ''
                ]"
                :data-id="deviceItem.id"
              >
                {{ deviceItem.name }}
              </view>
            </view>
            <view class="search_box">
              <u-search
                maxlength="15"
                shape="square"
                placeholder="自定义设备,不超过15个字"
                :show-action="true"
                action-text="添加"
                :animation="true"
                search-icon=""
                v-model="deviceName"
                @custom="addDevcie"
              ></u-search>
            </view>
          </view>
        </scroll-view>
      </view>
    </u-popup>
  </view>
</template>

<script>
export default {
  name: 'SelectMultiple',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    defaultValue: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      deviceName: '',
      deviceList: [],
      selectKeys: [],
      myValue: this.value
    };
  },
  created() {
    this.init();
  },
  methods: {
    open() {
      let selectKeys = [];
      if (this.defaultValue && this.defaultValue.length > 0) {
        this.defaultValue.forEach(e => {
          let data = this.deviceList.find(i => {
            return i.id == e;
          });
          if (data) {
            selectKeys.push(data);
          }
        });
      } else {
        this.deviceList.forEach(e => {
          if (`${e.defaultStatus}` === '1') {
            selectKeys.push(e);
          }
        });
      }
      this.selectKeys = selectKeys;
    },
    async init() {
      try {
        let deviceList = await this.ajax.getDeviceList();
        this.deviceList = deviceList.object || [];
      } catch (error) {}
    },
    handleChange(row) {
      const index = this.selectKeys.findIndex(e => {
        return e.id == row.id;
      });

      if (index > -1) {
        this.selectKeys.splice(index, 1);
      } else {
        this.selectKeys.push(row);
      }
    },
    // 新增设备
    async addDevcie() {
      let name = this.deviceName.trim();
      if (!name) return;
      if (
        this.deviceList.some(e => {
          return e.name === name;
        })
      ) {
        uni.showToast({
          icon: 'none',
          title: '设备名重复！'
        });
        return;
      }
      try {
        const res = await this.ajax.addBoardRoomDevice({
          name: name,
          icon: ''
        });
        const deviceList = await this.ajax.getDeviceList();
        this.deviceList = deviceList.object || [];
        this.selectKeys.push(this.deviceList[this.deviceList.length - 1]);
        this.deviceName = '';
      } catch (error) {
        throw error;
      }
    },
    close() {
      this.deviceName = '';
      this.selectKeys = [];
    },
    handleCancel() {
      this.myValue = false;
      this.$emit('confirm');
    },
    handleOk() {
      this.myValue = false;
      this.$emit('confirm', this.selectKeys);
    }
  },
  watch: {
    value: {
      handler(newValue) {
        this.myValue = newValue;
      },
      deep: true,
      immediate: true
    },
    myValue(newValue) {
      if (!newValue) this.myValue = false;
      this.$emit('input', newValue);
    }
  }
};
</script>
<style lang="scss" scoped>
.device_select {
  width: 100%;
  height: 100%;
  .device_select_container {
    .u-select__header {
      height: 80rpx;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 40rpx;
    }
    .device_select_content {
      padding: 0 40rpx;
      .content {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        .device_item {
          height: 48rpx;
          line-height: 44rpx;
          padding: 0 20rpx;
          background: #fafafa;
          border-radius: 44rpx;
          border: 2rpx solid #eee;
          font-size: 24rpx;
          color: #333333;
          text-align: center;
        }
        & > .device_item:last-child {
          margin-right: 0;
        }
        .active {
          color: white;
          background: #005bac;
        }
      }
      .search_box {
        padding: 24rpx 0;
      }
    }
  }
}
</style>
