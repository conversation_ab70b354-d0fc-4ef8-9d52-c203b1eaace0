<template>
  <view class="sign_in_container">
    <u-navbar
      class="header"
      :customBack="onClickBack"
      title="签到结果"
    ></u-navbar>
    <view class="content" v-if="info">
      <image :src="getImg" class="img" @click="toggle"></image>
      <view class="text"> {{ info.message }} </view>
      <view class="info-item">
        <span class="label">签到人: </span>
        <span class="value">{{ info.deptName }} {{ info.userName }}</span>
      </view>
      <view class="info-item">
        <span class="label">会议主题: </span>
        <span class="value">{{ info.motif }}</span>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      info: null
    };
  },
  onLoad(opt) {
    if (opt.meetingId) {
      this.ajax
        .signInReq({ meetingId: opt.meetingId, nd: opt.nd })
        .then(res => {
          this.info = res.object;
        });
    }
  },
  methods: {
    onClickBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    }
  },
  computed: {
    getImg() {
      if (this.info.message === '二维码已失效') {
        return require('@/assets/img/sign-error.png');
      }
      return require('@/assets/img/sign-success.png');
    }
  }
};
</script>
<style lang="scss" scoped>
.sign_in_container {
  height: 100%;
  display: flex;
  flex-direction: column;
  .content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .img {
      width: 240rpx;
      height: 240rpx;
      margin-bottom: 8rpx;
    }
    .text {
      margin-bottom: 88rpx;
      font-size: 54rpx;
      font-weight: 400;
      color: #333333;
    }
    .info-item {
      width: 100%;
      display: flex;
      align-items: center;
      padding: 0 8px;
      font-size: 18px;
      margin-bottom: 8px;
      .label {
        width: 120px;
        color: #666;
        display: inline-block;
        text-align: right;
        margin-right: 8px;
      }
      .value {
        flex: 1;
      }
    }
  }
}
</style>
