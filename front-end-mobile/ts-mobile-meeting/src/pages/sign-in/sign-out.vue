<template>
  <view class="sign_out_container">
    <u-navbar
      class="header"
      :customBack="onClickBack"
      title="签退结果"
    ></u-navbar>
    <view class="content" v-if="text">
      <image :src="getImg" class="img" @click="toggle"></image>
      <view class="text">
        {{ text }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      text: ''
    };
  },
  onLoad(opt) {
    if (opt.meetingId) {
      this.ajax.signOut({ meetingId: opt.meetingId, nd: opt.nd }).then(res => {
        this.text = res.object;
      });
    }
  },
  methods: {
    onClickBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    }
  },
  computed: {
    getImg() {
      if (this.text === '二维码已失效') {
        return require('@/assets/img/sign-error.png');
      }
      return require('@/assets/img/sign-success.png');
    }
  }
};
</script>
<style lang="scss" scoped>
.sign_out_container {
  height: 100%;
  display: flex;
  flex-direction: column;
  .content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .img {
      width: 180rpx;
      height: 180rpx;
      margin-bottom: 8rpx;
    }
    .text {
      margin-bottom: 88rpx;
      font-size: 54rpx;
      font-weight: 400;
      color: #333333;
    }
  }
}
</style>
