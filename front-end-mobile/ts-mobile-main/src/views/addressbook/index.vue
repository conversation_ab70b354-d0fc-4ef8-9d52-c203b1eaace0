<template>
  <div class="ts-container router-view">
    <div class="search-bar">
      <form class="search-input-form" action="/">
        <van-search
          class="search-input"
          v-model="searchVal"
          shape="round"
          placeholder="输入姓名、科室、电话搜索"
          @search="search"
          @input="search"
        />
      </form>
    </div>

    <div class="tabs-content">
      <van-tabs
        v-model="tabNum"
        animated
        @change="changeTab()"
        :before-change="beforeChange"
      >
        <van-tab title="内部联系人">
          <van-pull-refresh
            style="height: 100%;"
            v-model="refreshing0"
            @refresh="onRefresh()"
          >
            <div class="tab-list">
              <van-list
                v-model="loading0"
                :finished="finished0"
                finished-text="没有更多了"
                @load="onLoad()"
              >
                <div
                  class="user-cell"
                  v-for="(item, index) in contractArr0"
                  :key="index"
                  :class="
                    item.empSex == 0
                      ? 'user-cell-man'
                      : item.empSex == 1
                      ? 'user-cell-woman'
                      : ''
                  "
                  style="background:#fff;"
                >
                  <van-image
                    :src="item.avatar"
                    fit="cover"
                    lazy-load
                    class="user-avatar"
                    error-icon="user-o"
                    loading-icon="user-o"
                    @click="handleReviewAvatar(item)"
                  >
                  </van-image>
                  <div
                    class="user-info"
                    @click="viewInternalContact(item, index)"
                  >
                    <div class="user-name">{{ item.empName }}</div>
                    <div class="user-dept" v-if="item.empDeptName">
                      {{ item.empDeptName }}
                    </div>
                    <div class="user-post" v-if="item.positionName">
                      {{ item.positionName }}
                    </div>
                  </div>
                </div>
              </van-list>
            </div>
          </van-pull-refresh>
        </van-tab>

        <van-tab title="科室通讯录">
          <van-pull-refresh
            style="height: 100%;"
            v-model="refreshing1"
            @refresh="onRefresh()"
          >
            <div class="tab-list">
              <van-list
                v-model="loading1"
                :finished="finished1"
                finished-text="没有更多了"
                @load="onLoad()"
              >
                <div
                  class="user-cell"
                  v-for="(item, index) in contractArr1"
                  :key="index"
                  style="background:#fff;"
                >
                  <van-image
                    :src="require('../../assets/images/phone.png')"
                    fit="cover"
                    lazy-load
                    class="user-phone"
                  >
                  </van-image>
                  <div class="user-info dept-username-tel">
                    <div class="user-name">
                      {{ item.orgName }}{{ item.name ? ' - ' + item.name : '' }}
                    </div>
                    <div
                      class="user-tel"
                      v-if="item.tel"
                      @click.stop="showDeptAddress(item.tel)"
                    >
                      {{ item.tel }}
                    </div>
                  </div>
                </div>
              </van-list>
            </div>
          </van-pull-refresh>
        </van-tab>

        <van-tab title="系统群组">
          <van-pull-refresh
            style="height: 100%;"
            v-model="refreshing2"
            @refresh="onRefresh()"
          >
            <div class="tab-list">
              <van-list
                v-model="loading2"
                :finished="finished2"
                finished-text="没有更多了"
                @load="onLoad()"
              >
                <div class="collapse-card">
                  <div
                    class="collapse-item"
                    :class="{ 'shrink-item': isExist(index1, 2) }"
                    v-for="(item, index1) in contractArr2"
                    :key="index1"
                  >
                    <div
                      class="collapse-item-header bottom-line"
                      @click="collapse(item, index1)"
                    >
                      <van-icon name="arrow-down" class="collapse-icon" />
                      <p>{{ item.groupName }}</p>
                    </div>
                    <div
                      class="collapse-item-content"
                      style="background:#fcfcfc; border-bottom: 1px solid #e4e4e4;"
                    >
                      <div
                        class="user-cell"
                        v-for="(user, index) in item.employeeList"
                        :key="index"
                        :class="
                          user.empSex == 0
                            ? 'user-cell-man'
                            : user.empSex == 1
                            ? 'user-cell-woman'
                            : ''
                        "
                      >
                        <van-image
                          :src="user.avatar"
                          lazy-load
                          fit="cover"
                          class="user-avatar"
                          error-icon="user-o"
                          loading-icon="user-o"
                          @click="handleReviewAvatar(user)"
                        >
                        </van-image>
                        <div
                          class="user-info"
                          @click="viewGroupContact(index1, index)"
                        >
                          <div class="user-name">{{ user.empName }}</div>
                          <div class="user-dept" v-if="item.empDeptName">
                            {{ user.empDeptName }}
                          </div>
                          <div class="user-post" v-if="item.positionName">
                            {{ user.positionName }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </van-list>
            </div>
          </van-pull-refresh>
        </van-tab>

        <van-tab title="个人群组">
          <van-pull-refresh
            style="height: 100%;"
            v-model="refreshing3"
            @refresh="onRefresh()"
          >
            <div class="tab-list">
              <van-list
                v-model="loading3"
                :finished="finished3"
                finished-text="没有更多了"
                @load="onLoad"
              >
                <div class="collapse-card">
                  <div
                    class="collapse-item"
                    :class="{ 'shrink-item': isExist(index1, 3) }"
                    v-for="(item, index1) in contractArr3"
                    :key="index1"
                  >
                    <div
                      class="collapse-item-header bottom-line"
                      @click="collapse(item, index1)"
                    >
                      <van-icon name="arrow-down" class="collapse-icon" />
                      <p>{{ item.groupName }}</p>
                      <van-icon
                        name="setting-o"
                        class="group-setting"
                        @click="groupManage(item)"
                      />
                    </div>
                    <div
                      class="collapse-item-content"
                      style="background:#fcfcfc; border-bottom: 1px solid #e4e4e4;"
                    >
                      <div
                        class="user-cell"
                        v-for="(user, index) in item.employeeList"
                        :key="index"
                        :class="
                          user.empSex == 0
                            ? 'user-cell-man'
                            : user.empSex == 1
                            ? 'user-cell-woman'
                            : ''
                        "
                      >
                        <van-image
                          :src="user.avatar"
                          fit="cover"
                          lazy-load
                          class="user-avatar"
                          error-icon="user-o"
                          loading-icon="user-o"
                          @click="handleReviewAvatar(user)"
                        >
                        </van-image>

                        <div
                          class="user-info"
                          @click="viewGroupContact(index1, index)"
                        >
                          <div class="user-name">{{ user.empName }}</div>
                          <div class="user-dept" v-if="item.empDeptName">
                            {{ user.empDeptName }}
                          </div>
                          <div class="user-post" v-if="item.positionName">
                            {{ user.positionName }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </van-list>
            </div>
          </van-pull-refresh>
        </van-tab>

        <van-tab title="外部联系人">
          <van-pull-refresh
            style="height: 100%;"
            v-model="refreshing4"
            @refresh="onRefresh()"
          >
            <div class="tab-list">
              <van-list
                v-model="loading4"
                :finished="finished4"
                finished-text="没有更多了"
                @load="onLoad()"
              >
                <div
                  class="user-cell"
                  v-for="(item, index) in contractArr4"
                  :key="index"
                  :class="
                    item.empSex == 0
                      ? 'user-cell-man'
                      : item.empSex == 1
                      ? 'user-cell-woman'
                      : ''
                  "
                  style="background:#fff;"
                  @click="viewExternalContact(item, index)"
                >
                  <van-image
                    :src="item.avata"
                    fit="cover"
                    lazy-load
                    class="user-avatar"
                    error-icon="user-o"
                    loading-icon="user-o"
                  >
                  </van-image>

                  <div class="user-info">
                    <div class="user-name">{{ item.linkmanName }}</div>
                    <div class="user-dept" v-if="item.createDeptName">
                      {{ item.createDeptName }}
                    </div>
                    <div class="user-post" v-if="item.linkmanDuty">
                      {{ item.linkmanDuty }}
                    </div>
                  </div>
                </div>
              </van-list>
            </div>
          </van-pull-refresh>
        </van-tab>

        <van-tab title="科室值班通讯录">
          <van-pull-refresh
            style="height: 100%;"
            v-model="refreshing5"
            @refresh="onRefresh()"
          >
            <div class="tab-list">
              <div class="search-box">
                <div>
                  <span class="search-time today" @click="handleSeachTodayDuty"
                    >今</span
                  >
                  <span class="search-time" @click="dutyCalendarShow = true"
                    >值班时间 {{ starttime }}
                    {{ starttime && endtime ? '~' : '' }} {{ endtime }}</span
                  >
                </div>
                <van-icon
                  v-if="starttime && endtime"
                  name="cross"
                  @click="handleClearDutyTime"
                />
              </div>
              <van-list
                v-model="loading5"
                :finished="finished5"
                finished-text="没有更多了"
                @load="onLoad()"
              >
                <div
                  class="user-cell duty"
                  v-for="(item, index) in contractArr5"
                  :key="index"
                  :class="
                    item.empSex == 0
                      ? 'user-cell-man'
                      : item.empSex == 1
                      ? 'user-cell-woman'
                      : ''
                  "
                  style="background:#fff;"
                  @click="viewDutyDetailsPerson(item, index)"
                >
                  <van-image
                    :src="item.avata"
                    fit="cover"
                    lazy-load
                    class="user-avatar"
                    error-icon="user-o"
                    loading-icon="user-o"
                  >
                  </van-image>

                  <div class="user-info">
                    <div class="user-name">
                      <span class="small">{{ item.deptName }}</span>
                      {{ item.dutyPerson }}
                    </div>
                    <div class="user-post" v-if="item.starttime">
                      {{ item.starttime }}~{{ item.endtime }}
                    </div>
                  </div>
                </div>
              </van-list>
            </div>
          </van-pull-refresh>
        </van-tab>
      </van-tabs>
    </div>

    <van-calendar
      v-model="dutyCalendarShow"
      title="选择日期区间"
      type="range"
      @confirm="dutyConfirm"
      color="#005BAC"
      :min-date="minDate"
    />

    <van-action-sheet
      description="联系电话"
      v-model="dialogDeptAddress"
      :actions="deptAddressList"
      cancel-text="取消"
      close-on-click-action
      @select="callDeptAddress"
    />

    <van-action-sheet
      v-model="dialogDutyPersonDetails"
      :title="dutyPersonTitle"
    >
      <div class="duty-details-content">
        <ul class="info-ul">
          <li class="item">
            <span class="label">值班科室</span>
            <span class="value">{{ viewDutyDetailsEach.deptName }}</span>
          </li>
          <li class="item">
            <span class="label">值班时间</span>
            <span class="value" style="white-space: pre-wrap;"
              >{{
                viewDutyDetailsEach.starttime +
                  ' ' +
                  tipsN +
                  viewDutyDetailsEach.endtime
              }}
            </span>
          </li>
          <li class="item" @click="call(viewDutyDetailsEach.telephone)">
            <span class="label">值班电话</span>
            <span class="value iphone">{{
              viewDutyDetailsEach.telephone
            }}</span>
          </li>
          <li class="item">
            <span class="label">值班地点</span>
            <span class="value">{{ viewDutyDetailsEach.place }}</span>
          </li>
          <div class="pengding-height"></div>
          <li class="item">
            <span class="label">值班领导</span>
            <span class="value">{{ viewDutyDetailsEach.leaderPerson }}</span>
          </li>
          <li class="item" @click="call(viewDutyDetailsEach.leaderTelephone)">
            <span class="label">值班领导电话</span>
            <span class="value iphone">{{
              viewDutyDetailsEach.leaderTelephone
            }}</span>
          </li>
          <li class="item">
            <span class="label">备注</span>
            <span class="value"></span>
          </li>
          <div class="remake">
            {{ viewDutyDetailsEach.remark }}
          </div>
        </ul>
      </div>
    </van-action-sheet>
  </div>
</template>

<script>
import index from './index.js';
import { PullRefresh, Tabs, Tab, Image, Search } from 'vant';
import { Image as VanImage } from 'vant';
const d = new Date();
export default {
  name: 'index',
  data() {
    return {
      dutyPersonTitle: '',
      tipsN: '\n/',
      dialogDutyPersonDetails: false,
      dutyCalendarShow: false,
      minDate: new Date(d.getFullYear(), d.getMonth() - 5, 1),
      starttime: '',
      endtime: '',
      viewDutyDetailsEach: {},
      dialogDeptAddress: false,
      deptAddressList: []
    };
  },
  methods: {
    handleSeachTodayDuty() {
      let today = this.$dayjs().format('YYYY-MM-DD');
      this.starttime = today;
      this.endtime = today;

      this.search();
    },
    dutyConfirm(e) {
      const start = this.$dayjs(e[0]).format('YYYY-MM-DD');
      const end = this.$dayjs(e[1]).format('YYYY-MM-DD');

      this.starttime = start;
      this.endtime = end;
      this.dutyCalendarShow = false;

      this.search();
    },
    handleClearDutyTime() {
      this.starttime = '';
      this.endtime = '';

      this.search();
    },
    call(phone) {
      let phoneReg = new RegExp(/^1[3-9][0-9]{9}$/g); //手机号校验规则
      if (phoneReg.test(phone)) {
        window.location.href = 'tel:' + phone;
      } else {
        this.$toast('号码错误');
      }
    },
    viewDutyDetailsPerson(item) {
      this.dialogDutyPersonDetails = true;
      this.viewDutyDetailsEach = item;
      this.dutyPersonTitle = item.dutyPerson + '的值班详情';
    },
    showDeptAddress(tel) {
      let list = tel
        .trim()
        .replace(/[^0-9,]/g, '')
        .split(',')
        .filter(item => item);

      this.deptAddressList = list.map(i => {
        return {
          name: i
        };
      });
      this.dialogDeptAddress = true;
    },
    callDeptAddress(item) {
      window.location.href = 'tel:' + item.name;
    }
  },
  mixins: [index],
  components: {
    [PullRefresh.name]: PullRefresh,
    [Tabs.name]: Tabs,
    [Tab.name]: Tab,
    [VanImage.name]: VanImage,
    [Search.name]: Search
  }
};
</script>

<style scoped lang="scss">
.ts-container {
  display: flex;
  flex-direction: column;
  .search-bar {
    padding: 5px 16px;
    background-color: #fff;
    position: fixed;
    top: 0;
    z-index: 999;
    width: calc(100% - 32px);
    display: flex;
    .search-input-form {
      flex: 1;
      .search-input {
        height: 34px;
        width: 100%;
        font-size: 14px;
      }
    }
    .add-btn {
      width: 38px;
      text-align: right;
      font-size: 22px;
      line-height: 36px;
      color: #979797;
    }
  }
  .tabs-content {
    flex: 1;
    margin-top: 44px;
    background-color: $main-bg;
    /deep/.van-tabs__wrap {
      position: fixed;
      top: 44px;
      z-index: 999;
      width: 100%;
      &::after {
        content: ' ';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 1px;
        background-color: #e4e4e4;
        transform: scaleY(0.5);
      }
    }
    .tab-list {
      margin: 44px 0;
      min-height: calc(100vh - 150px);
      .search-box {
        width: 100%;
        height: 40px;
        background: #fff;
        padding: 0 8px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .search-time {
          padding: 2px 16px;
          background: #cdcdcd;
          border: 1px solid #eee;
          border-radius: 20px;
          background: #f3f3f3;
          font-size: 14px;
          &.today {
            margin-right: 8px;
          }
        }
      }
    }
    .user-cell-woman /deep/.van-image__error {
      background-color: $sexwoman-color;
      .van-image__error-icon {
        color: #fff;
      }
    }
    .user-cell-man /deep/.van-image__error {
      background-color: $sexman-color;
      .van-image__error-icon {
        color: #fff;
      }
    }
    .user-cell {
      height: 40px;
      padding: 8px 16px;
      position: relative;
      display: flex;
      align-items: center;
      overflow: hidden;
      /deep/.van-image__error-icon {
        font-size: 24px;
      }
      /deep/.van-image__loading-icon {
        font-size: 24px;
      }
      .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        overflow: hidden;
      }
      .user-phone {
        height: 25px;
        width: 25px;
        margin-top: 8px;
        border-radius: 50%;
        overflow: hidden;
      }
      .user-info {
        flex: 1;
        display: inline-block;
        height: 40px;
        vertical-align: top;
        overflow: hidden;
        margin-left: 8px;
        &.dept-username-tel {
          min-height: 40px;
          height: auto;
        }
        .user-name {
          font-size: 16px;
          color: #333;
          .small {
            font-size: 12px;
            color: #666;
          }
        }
        .user-dept,
        .user-post {
          font-size: 12px;
          display: inline-block;
          margin-top: 2px;
          color: #666;
          margin-right: 12px;
        }
        .user-tel {
          flex: 1;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
    }
  }
  .collapse-card {
    .collapse-item {
      height: auto;
      overflow: hidden;
      .collapse-item-header {
        height: 44px;
        display: flex;
        align-items: center;

        padding: 0 16px;
        color: #333;
        background-color: #fff;
        position: relative;
        .collapse-icon {
          font-size: 14px;
          margin-right: 10px;
          color: #cdcdcd;
          font-weight: bold;
        }
        p {
          flex: 1;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
        .group-setting {
          color: $theme-color;
          font-size: 18px;
          float: right;
        }
      }
      .collapse-item-content {
        padding: 8px 0;
      }
    }
    .shrink-item {
      height: 44px;
      .collapse-icon {
        transform: rotate(-90deg);
      }
    }
  }
  .duty-details-content {
    padding: 8px 0;
    width: 100%;
    .info-ul {
      width: 100%;
      overflow: hidden;
      .item {
        padding: 0 8px;
        min-height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .label {
          display: inline-block;
          width: 120px;
        }
        .value {
          width: 100%;
          text-align: right;
          font-size: 12px;
          color: #666;
          &.iphone {
            color: #005bac;
          }
        }
      }
      .pengding-height {
        height: 6px;
        width: 100%;
        background-color: #f2f2f2;
      }
      .remake {
        width: 100%;
        min-height: 50px;
        max-height: 150px;
        padding: 0 8px;
        font-size: 12px;
        color: #666;
      }
    }
  }
}
/deep/.van-tabs__line {
  background-color: $theme-color;
}
/deep/.van-pull-refresh {
  min-height: 50px;
}
/deep/.van-action-sheet__description {
  padding: 10px 16px;
}
</style>
